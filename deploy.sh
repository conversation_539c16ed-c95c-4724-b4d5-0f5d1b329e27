#!/bin/bash
rm -rf /Users/<USER>/ideaProjects/boot-admin-ui/dist
rm -rf /Users/<USER>/ideaProjects/boot-admin-ui/dist.zip

echo "开始构建项目..."
vue-cli-service build
echo "构建完成，开始压缩..."

zip -r dist.zip dist
# 部署脚本 - 将文件上传到远程服务器
echo "开始上传dist.zip文件到远程服务器..."
scp /Users/<USER>/ideaProjects/boot-admin-ui/dist.zip root@*************:/www/boot-admin/ui
ssh root@************* "cd /www/boot-admin/ui && rm -rf dist && unzip -o dist.zip && rm -f dist.zip"

# 检查上传结果
if [ $? -eq 0 ]; then
  echo "上传成功!"
else
  echo "上传失败，请检查网络连接和服务器状态。"
fi
