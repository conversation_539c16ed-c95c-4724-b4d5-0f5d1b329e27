// element 样式补丁
.el-card {
  &.is-always-shadow {
    box-shadow: 0 0 8px 0 rgba(232,237,250,.6), 0 2px 4px 0 rgba(232,237,250,.5);
  }
  &.is-hover-shadow {
    &:hover {
      box-shadow: 0 0 8px 0 rgba(232,237,250,.6), 0 2px 4px 0 rgba(232,237,250,.5);
    }
  }
}

.el-menu--horizontal {
  border-bottom: none !important;
}

// 标签页样式优化
.el-tabs {
  &__header {
    margin-bottom: 15px;
  }

  &__item {
    height: 40px;
    line-height: 40px;
    transition: color 0.25s, background-color 0.25s, border-color 0.25s !important;
  }

  &__nav-next, &__nav-prev {
    line-height: 40px;
    font-size: 16px;
  }
}

.el-tabs__item:focus.is-active.is-focus:not(:active) {
  box-shadow: none !important;
}

// 确保标签页关闭按钮位置固定
.el-tabs__item {
  padding-right: 32px !important;
}

.el-tabs__item .el-icon-close {
  position: absolute !important;
  right: 10px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  margin: 0 !important;
  height: 16px !important;
  width: 16px !important;
  line-height: 16px !important;
  background-color: rgba(0, 0, 0, 0.05) !important;
  border-radius: 50% !important;
  text-align: center !important;
  transition: background-color 0.2s, color 0.2s !important;

  &:hover {
    background-color: rgba(0, 0, 0, 0.15) !important;
    color: #333 !important;
  }
}

// 修复IE宽度不能撑满
.el-table__body,
.el-table__header {
  width: 100% !important;
}

// Chrome下表格头部错位修复
.el-table th.gutter,
.el-table colgroup.gutter {
  display: table-cell !important;
}

// 输入框美式设计风格优化
.el-input, .el-textarea {
  // 输入框基础样式
  .el-input__inner, .el-textarea__inner {
    padding: 8px 12px;
    height: 36px;
    border-radius: 6px;
    transition: all 0.25s ease;
    letter-spacing: 0.01em;
    font-size: 14px;
    line-height: 1.6;
    color: #1E293B;
    background-color: #ffffff;
    border: none; // 默认无边框
    border-bottom: 1px solid #E4E7ED; // 只保留底部边框
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);

    &::placeholder {
      color: #9CA3AF;
      font-weight: 400;
      opacity: 0.8;
    }

    &:hover {
      border-bottom-color: #CDD5DF;
    }

    &:focus {
      border: 2px solid #0d47a1; // 选中时显示较粗的边框(2px)
      box-shadow: 0 0 0 3px rgba(13, 71, 161, 0.15);
      outline: none;
    }
  }

  // 禁用状态
  &.is-disabled .el-input__inner {
    background-color: #F8FAFC;
    color: #9CA3AF;
    cursor: not-allowed;
    border-bottom: 1px solid #E4E7ED;
  }

  // 成功状态
  &.is-success .el-input__inner {
    border-color: #10b981;
    border: 2px solid #10b981; // 成功状态边框也调整为2px
  }

  // 错误状态
  &.is-error .el-input__inner {
    border-color: #ef4444;
    border: 2px solid #ef4444; // 错误状态边框也调整为2px
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.15);
  }

  // 前后缀图标
  .el-input__prefix, .el-input__suffix {
    color: #9CA3AF;

    .el-input__icon {
      line-height: 36px;
      transition: all 0.25s ease;
    }
  }

  // 调整前缀图标位置和间距
  .el-input__prefix {
    left: 8px;

    .el-input__icon {
      padding-right: 5px;
    }
  }

  // 调整后缀图标位置
  .el-input__suffix {
    right: 8px;
  }

  &:hover .el-input__prefix, &:hover .el-input__suffix {
    color: #606266;
  }

  // 调整带图标的输入框内边距
  &--prefix .el-input__inner {
    padding-left: 30px;
  }

  &--suffix .el-input__inner {
    padding-right: 30px;
  }

  // 搜索类型输入框
  &.el-input--search {
    .el-input__inner {
      padding-right: 35px;
    }

    .el-input__suffix {
      right: 8px;
    }
  }
}

// 文本域样式优化
.el-textarea {
  .el-textarea__inner {
    padding: 8px 12px;
    min-height: 60px;
    line-height: 1.6;
    border-radius: 6px;
    border: none; // 默认无边框
    border-bottom: 1px solid #E4E7ED; // 只保留底部边框

    &:focus {
      border: 2px solid #0d47a1; // 选中时显示较粗的边框(2px)
      box-shadow: 0 0 0 3px rgba(13, 71, 161, 0.15);
    }
  }
}

// 下拉选择器样式优化
.el-select {
  .el-input__inner {
    cursor: pointer;
  }

  .el-input.is-focus .el-input__inner {
    border: 2px solid #0d47a1; // 选中时显示较粗的边框(2px)
    box-shadow: 0 0 0 3px rgba(13, 71, 161, 0.15);
  }

  .el-select__tags {
    padding: 6px 0 0 12px;
  }
}

// 日期选择器样式优化
.el-date-editor {
  &.el-input, &.el-input__inner {
    width: 100%;
  }

  .el-range-separator {
    padding: 0 6px;
    line-height: 36px;
  }
}

// 表格通用优化
.el-table {
  // 提高表格边框的一致性
  border-collapse: collapse;
  background-color: #ffffff;

  // 表格外框样式
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);

  // 去除表格外边框
  &::before, &::after {
    display: none;
  }

  // 平滑过渡效果
  th, td {
    transition: all 0.2s ease;
  }

  // 调整单元格边距
  td, th {
    padding: 12px;
  }

  // 调整行高和文字间距
  .cell {
    line-height: 1.5;
    padding-left: 10px;
    padding-right: 10px;
    word-break: break-word;
    letter-spacing: 0.01em;
    display: flex;
    align-items: center;
  }

  // 改进表格行样式
  .el-table__row {
    transition: all 0.2s ease;

    &:hover {
      background-color: #f5f7fa;
    }
  }

  // 更美观的表格头部
  .el-table__header-wrapper {
    th {
      background-color: #f5f7fa;
      border-bottom: 1px solid #ebeef5;

      .cell {
        font-weight: 500;
        color: #606266;
      }
    }
  }

  // 改进复选框在表格中的对齐方式
  .el-checkbox {
    margin-right: 8px;
  }

  // 优化表格底部边框
  &::after {
    background-color: transparent;
  }

  // 改进表格行样式
  .el-table__body tr {
    td {
      border-bottom: 1px solid #ebeef5;
    }

    &:last-child td {
      border-bottom: none;
    }
  }

  // 调整表格内容垂直居中
  .el-table__cell {
    vertical-align: middle;
  }

  // 排序图标样式优化
  .caret-wrapper {
    height: 30px;

    .sort-caret {
      border-width: 5px;

      &.ascending {
        top: 7px;
      }

      &.descending {
        bottom: 7px;
      }
    }
  }

  // 状态圆点样式
  .status-dot {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;

    &.success {
      background-color: #10b981;
      box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
    }

    &.info {
      background-color: #9ca3af;
      box-shadow: 0 0 0 2px rgba(156, 163, 175, 0.2);
    }

    &.warning {
      background-color: #f59e0b;
      box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
    }

    &.danger {
      background-color: #ef4444;
      box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
    }
  }

  // 头像样式
  img.table-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 8px;
    border: 1px solid #ebeef5;
  }

  // 选中行样式
  .el-table__body tr.current-row>td {
    background-color: rgba(16, 185, 129, 0.1);
  }
}

// 全局标签样式优化 - 椭圆形设计
.el-tag {
  border-radius: 12px;
  padding: 0 12px;
  height: 24px;
  line-height: 22px;
  border-width: 0;

  // 标签类型样式
  &--success {
    background-color: rgba(16, 185, 129, 0.1);
    border-color: transparent;
    color: #10b981;
  }

  &--info {
    background-color: rgba(96, 165, 250, 0.1);
    border-color: transparent;
    color: #3b82f6;
  }

  &--warning {
    background-color: rgba(245, 158, 11, 0.1);
    border-color: transparent;
    color: #f59e0b;
  }

  &--danger {
    background-color: rgba(239, 68, 68, 0.1);
    border-color: transparent;
    color: #ef4444;
  }

  &--primary {
    background-color: rgba(13, 71, 161, 0.1);
    border-color: transparent;
    color: #0d47a1;
  }
}
