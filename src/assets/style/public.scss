@import '~@/assets/style/unit/color.scss';

// 工具类名统一前缀
$prefix: d2;

// 禁止用户选中 鼠标变为手形
%unable-select {
  user-select: none;
  cursor: pointer;
}

// 填满父元素
// 组要父元素 position: relative | absolute;
%full {
  position: absolute;
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
}

// flex 垂直水平居中
%flex-center-row {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: row;
}
%flex-center-col {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

// 将元素模拟成卡片外观
%card {
  border: 1px solid #dddee1;
  border-color: #e9eaec;
  background: #fff;
  border-radius: 4px;
  font-size: 14px;
  position: relative;
}

.button {
  float: right;
}

.filter-item {
  display: inline-block;
  vertical-align: middle;
}

.el-dialog__body {
  overflow: auto;
}
.dialog-main-tree {
  max-height: 400px;
  overflow-y: auto;
}
.el-table th {
  word-break: break-word;
  color: rgba(0, 0, 0, 0.85);
  background-color: #fafafa;
}
.avue-crud__menu {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  position: relative;
  width: 100%;
  min-height: 40px;
  height: auto;
  overflow: hidden;
  margin-bottom: 5px;
}

.demo-table-expand {
  font-size: 0;
}
.demo-table-expand label {
    width: 70px;
    color: #99a9bf;
}
.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 50%;
}
.demo-table-expand span{
  display: block;
  word-break: break-all;
  word-wrap: break-word;
}