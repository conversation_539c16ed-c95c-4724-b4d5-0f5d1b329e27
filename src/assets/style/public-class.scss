@import 'public';

// 补丁 base
@import '~@/assets/style/fixed/base.scss';
// 补丁 element
@import '~@/assets/style/fixed/element.scss';
// 补丁 markdown
@import '~@/assets/style/fixed/markdown.scss';
// 补丁 n-progress
@import '~@/assets/style/fixed/n-progress.scss';
// 补丁 vue-splitpane
@import '~@/assets/style/fixed/vue-splitpane.scss';
// 补丁 vue-grid-layout
@import '~@/assets/style/fixed/vue-grid-layout.scss';
// 补丁 tree-view
@import '~@/assets/style/fixed/tree-view.scss';

// 动画
@import '~@/assets/style/animate/vue-transition.scss';

// 在这里写公用的class
// 注意 这个文件里只写class
// mixin等内容请在 public.scss 里书写

// 文字相关
.#{$prefix}-text-center {
  text-align: center;
}

// 浮动相关
.#{$prefix}-fl {
  float: left;
}
.#{$prefix}-fr {
  float: right;
}

// 边距相关
$sizes: (0, 5, 10, 15, 20);

@for $index from 1 to 6 {
  .#{$prefix}-m-#{nth($sizes, $index)} { margin: #{nth($sizes, $index)}px !important; }
  .#{$prefix}-mt-#{nth($sizes, $index)} { margin-top: #{nth($sizes, $index)}px !important; }
  .#{$prefix}-mr-#{nth($sizes, $index)} { margin-right: #{nth($sizes, $index)}px !important; }
  .#{$prefix}-mb-#{nth($sizes, $index)} { margin-bottom: #{nth($sizes, $index)}px !important; }
  .#{$prefix}-ml-#{nth($sizes, $index)} { margin-left: #{nth($sizes, $index)}px !important; }

  .#{$prefix}-p-#{nth($sizes, $index)} { padding: #{nth($sizes, $index)}px !important; }
  .#{$prefix}-pt-#{nth($sizes, $index)} { padding-top: #{nth($sizes, $index)}px !important; }
  .#{$prefix}-pr-#{nth($sizes, $index)} { padding-right: #{nth($sizes, $index)}px !important; }
  .#{$prefix}-pb-#{nth($sizes, $index)} { padding-bottom: #{nth($sizes, $index)}px !important; }
  .#{$prefix}-pl-#{nth($sizes, $index)} { padding-left: #{nth($sizes, $index)}px !important; }
}

// 快速使用

.#{$prefix}-m { margin: 20px !important; }
.#{$prefix}-mt { margin-top: 20px !important; }
.#{$prefix}-mr { margin-right: 20px !important; }
.#{$prefix}-mb { margin-bottom: 20px !important; }
.#{$prefix}-ml { margin-left: 20px !important; }

.#{$prefix}-p { padding: 20px !important; }
.#{$prefix}-pt { padding-top: 20px !important; }
.#{$prefix}-pr { padding-right: 20px !important; }
.#{$prefix}-pb { padding-bottom: 20px !important; }
.#{$prefix}-pl { padding-left: 20px !important; }
