/* 美式设计风格字体定义 */

/* 导入Google Fonts (如果部署环境允许访问Google) */
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

/* 定义字体变量 */
$font-family-primary: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
$font-family-secondary: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif;
$font-family-monospace: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;

/* 字体粗细 */
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

/* 字体大小 */
$font-size-base: 14px;
$font-size-small: 12px;
$font-size-medium: 16px;
$font-size-large: 18px;
$font-size-xlarge: 22px;
$font-size-xxlarge: 26px;

/* 行高 */
$line-height-tight: 1.2;
$line-height-normal: 1.5;
$line-height-loose: 1.8;

/* 字间距 */
$letter-spacing-normal: normal;
$letter-spacing-wide: 0.05em;
$letter-spacing-tight: -0.01em;

/* 应用到Element UI变量 */
$--font-path: 'element-ui/lib/theme-chalk/fonts';
$--font-family: $font-family-primary; 