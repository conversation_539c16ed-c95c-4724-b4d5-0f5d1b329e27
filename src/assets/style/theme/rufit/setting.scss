// 主题名称
$theme-name: 'rufit';
// 主题背景颜色
$theme-bg-color: #f5f7fa;
// 主题背景图片遮罩
$theme-bg-mask: rgba(#000, 0);

// 消息提示
$theme-message-info-background-color: $color-bg;
$theme-message-info-text-color: $color-text-normal;
$theme-message-info-border-color: $color-border-1;

// container组件
$theme-container-background-color: #FFF;
$theme-container-header-footer-background-color: #FFF;
$theme-container-border-inner: 1px solid $color-border-2;
$theme-container-border-outer: 1px solid #cfd7e5;

$theme-multiple-page-control-color: #000000;
$theme-multiple-page-control-color-active: #000000;
$theme-multiple-page-control-nav-prev-color: #000000;
$theme-multiple-page-control-nav-next-color: #000000;
$theme-multiple-page-control-border-color: #e4e7ed;
$theme-multiple-page-control-border-color-active: #000000;
$theme-multiple-page-control-background-color: transparent;
$theme-multiple-page-control-background-color-active: transparent;

// 顶栏和侧边栏中展开的菜单 hover 状态下
$theme-menu-item-color-hover: #293849;
$theme-menu-item-background-color-hover: #0d47a1;

// 顶栏上的文字颜色
$theme-header-item-color: #000000;
$theme-header-item-background-color: transparent;
// 顶栏上的项目在 hover 时
$theme-header-item-color-hover: #0d47a1;
$theme-header-item-background-color-hover: rgba(#0d47a1, .05);
// 顶栏上的项目在 focus 时
$theme-header-item-color-focus: #0d47a1;
$theme-header-item-background-color-focus: rgba(#0d47a1, .1);
// 顶栏上的项目在 active 时
$theme-header-item-color-active: #0d47a1;
$theme-header-item-background-color-active: rgba(#0d47a1, .1);

// 侧边栏上的文字颜色
$theme-aside-item-color: #000000;
$theme-aside-item-background-color: transparent;
// 侧边栏上的项目在 hover 时
$theme-aside-item-color-hover: #000000;
$theme-aside-item-background-color-hover: rgba(#0d47a1, .1);
// 侧边栏上的项目在 focus 时
$theme-aside-item-color-focus: #FFFFFF;
$theme-aside-item-background-color-focus: rgba(#0d47a1, .8);
// 侧边栏上的项目在 active 时
$theme-aside-item-color-active: #FFFFFF;
$theme-aside-item-background-color-active: #0d47a1;

// 侧边栏菜单为空的时候显示的元素
$theme-aside-menu-empty-icon-color: #909399;
$theme-aside-menu-empty-text-color: #909399;
$theme-aside-menu-empty-background-color: rgba(#000, .03);
$theme-aside-menu-empty-icon-color-hover: #0d47a1;
$theme-aside-menu-empty-text-color-hover: #0d47a1;
$theme-aside-menu-empty-background-color-hover: rgba(#0d47a1, .05);

// 按钮颜色
$--color-primary: #0d47a1;
$--button-primary-background-color: #0d47a1;
$--button-primary-border-color: #0d47a1;
$--color-success: #0d47a1;
$--button-success-background-color: #0d47a1;
$--button-success-border-color: #0d47a1;

// Element UI 全局颜色变量
$--color-primary: #0d47a1;
$--color-success: #0d47a1;
$--color-warning: #e6a23c;
$--color-danger: #f56c6c;
$--color-info: #0d47a1;
$--color-text-primary: #303133;
$--color-text-regular: #606266;
$--color-text-secondary: #909399;
$--color-text-placeholder: #C0C4CC;
$--border-color-base: #DCDFE6;
$--border-color-light: #E4E7ED;
$--border-color-lighter: #EBEEF5;
$--border-color-extra-light: #F2F6FC;
$--background-color-base: #F5F7FA;

// 各组件变量
// Menu
$--menu-item-color: #1E293B;
$--menu-active-color: #FFFFFF;
$--menu-background-color-light: #f5f7fa;
$--menu-item-hover-fill: rgba(#0d47a1, .1);
$--menu-item-active-fill: #0d47a1;

// Button
$--button-hover-tint-percent: 20%;
$--button-active-shade-percent: 10%;

// Input
$--input-focus-border: #0d47a1;
$--input-hover-border: #0d47a1;

// Checkbox
$--checkbox-checked-color: #0d47a1;
$--checkbox-checked-border-color: #0d47a1;
$--checkbox-disabled-checked-color: #c2c2c2;

// Radio
$--radio-checked-color: #0d47a1;
$--radio-checked-border-color: #0d47a1;
$--radio-disabled-checked-color: #c2c2c2;

// Switch
$--switch-on-color: #0d47a1;

// Slider
$--slider-main-background-color: #0d47a1;

// Tag
$--tag-primary-color: #0d47a1;
$--tag-success-color: #0d47a1;

// Pagination
$--pagination-hover-color: #0d47a1;
$--pagination-button-color: #0d47a1;

// Table
$--table-header-background: #f5f7fa;
$--table-row-hover-background: rgba(#0d47a1, .05);
$--table-fixed-box-shadow: 0 0 10px rgba(0, 0, 0, .1);

// Loading
$--loading-spinner-color: #0d47a1;

// Upload
$--upload-dragger-hover-border-color: #0d47a1;

// Dialog
$--dialog-title-color: #303133;
$--dialog-close-hover-color: #0d47a1;

// DatePicker
$--datepicker-active-color: #0d47a1;
$--datepicker-hover-color: #e6f3eb;

// Select
$--select-option-selected-color: #0d47a1;
$--select-border-color-hover: #0d47a1;

// Form
$--form-label-color: #606266;
$--form-item-error-color: #f56c6c;
