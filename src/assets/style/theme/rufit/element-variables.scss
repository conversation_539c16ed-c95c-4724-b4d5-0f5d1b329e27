/* Element UI 变量定义 */
@import './fonts.scss';

/* 颜色变量 */
$--color-primary: #0d47a1;
$--color-success: #0d47a1;
$--color-warning: #e6a23c;
$--color-danger: #f56c6c;
$--color-info: #0d47a1;
$--color-white: #fff;

/* 字体变量 */
$--font-path: '~element-ui/lib/theme-chalk/fonts';
$--font-family: $font-family-primary;

/* 字体大小 */
$--font-size-base: $font-size-base;
$--font-size-small: $font-size-small;
$--font-size-large: $font-size-large;
$--font-weight-primary: $font-weight-normal;

/* 边框和阴影 */
$--border-color-light: #e4e7ed;
$--border-color-lighter: #ebeef5;
$--border-color-extra-light: #f2f6fc;

/* 组件变量 */
// 按钮
$--button-font-weight: $font-weight-medium;
$--button-primary-font-color: #fff;
$--button-primary-background-color: $--color-primary;
$--button-primary-border-color: $--color-primary;
$--button-success-font-color: #fff;
$--button-success-background-color: $--color-success;
$--button-success-border-color: $--color-success;

// 输入框
$--input-font-size: $font-size-base;
$--input-height: 36px;
$--input-border-radius: 6px;
$--input-border-color: transparent;
$--input-border: none;
$--input-border-bottom: 1px solid #e4e7ed;
$--input-focus-border: 2px solid $--color-primary;
$--input-hover-border: none;
$--input-hover-border-bottom: 1px solid #cdd5df;
$--input-placeholder-color: #9ca3af;
$--input-max-width: 100%;
$--input-padding-vertical: 8px;
$--input-padding-horizontal: 12px;
$--input-background-color: #ffffff;
$--input-clear-hover-color: #909399;
$--input-focus-fill: #ffffff;
$--input-disabled-fill: #f8fafc;
$--input-disabled-border: none;
$--input-disabled-border-bottom: 1px solid #e4e7ed;
$--input-disabled-color: #9ca3af;
$--input-disabled-placeholder-color: #c0c4cc;
$--input-hover-border-color: transparent;
$--input-hover-border-bottom-color: #cdd5df;
$--input-clear-color: #c0c4cc;
$--input-icon-color: #9ca3af;
$--input-prefix-padding: 30px;
$--input-suffix-padding: 30px;
$--input-prefix-width: 30px;
$--input-suffix-width: 30px;

// 单选框
$--radio-checked-font-color: $--color-primary;
$--radio-checked-input-border-color: $--color-primary;
$--radio-checked-input-background-color: $--color-primary;
$--radio-font-size: $font-size-base;

// 复选框
$--checkbox-checked-font-color: $--color-primary;
$--checkbox-checked-input-border-color: $--color-primary;
$--checkbox-checked-input-background-color: $--color-primary;
$--checkbox-font-size: $font-size-base;

// 开关
$--switch-on-color: $--color-primary;
$--switch-off-color: #c0ccda;

// 滑块
$--slider-main-background-color: $--color-primary;
$--slider-stop-background-color: $--color-white;

// 标签页
$--tab-font-color: #303133;
$--tab-active-color: $--color-primary;

// 菜单
$--menu-item-font-color: #303133;
$--menu-active-color: $--color-primary;
$--menu-item-font-size: $font-size-base;

// 表格 - 根据第二个截图调整
$--table-border-color: #ebeef5;
$--table-header-background-color: #f5f7fa;
$--table-header-font-color: #606266;
$--table-row-hover-background-color: #f5f7fa;
$--table-font-size: $font-size-base;
$--table-header-font-size: $font-size-base;
$--table-current-row-background-color: rgba(16, 185, 129, 0.1);

// 分页
$--pagination-font-color: #303133;
$--pagination-button-color: #303133;
$--pagination-background-color: $--color-white;
$--pagination-hover-color: $--color-primary;
$--pagination-font-size: $font-size-base;

// 对话框
$--dialog-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
$--dialog-title-font-color: #303133;
$--dialog-font-size: $font-size-base;
$--dialog-title-font-size: $font-size-large;
$--dialog-padding-primary: 20px;

// 弹出框
$--popover-title-font-color: #303133;
$--popover-title-font-size: $font-size-medium;
$--popover-font-size: $font-size-base;

// 加载指示器
$--loading-spinner-color: $--color-primary;
$--loading-fullscreen-spinner-color: $--color-primary;

// 消息
$--message-close-hover-color: #909399;
$--message-font-size: $font-size-base;

// 通知
$--notification-title-color: #303133;
$--notification-close-hover-color: #909399;
$--notification-font-size: $font-size-base;
$--notification-title-font-size: $font-size-medium;

/* 导入 Element UI 主题 */
@import "~element-ui/packages/theme-chalk/src/index";
