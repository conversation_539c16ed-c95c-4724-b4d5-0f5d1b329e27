@import './setting.scss';
@import './fonts.scss';
@import './element-variables.scss';
@import '../theme.scss';

/* 全局字体应用 */
body {
  font-family: $font-family-secondary;
  font-weight: $font-weight-normal;
  font-size: $font-size-base;
  line-height: $line-height-normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #303133;
}

h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6 {
  font-family: $font-family-primary;
  font-weight: $font-weight-semibold;
  line-height: $line-height-tight;
  margin-bottom: 0.5em;
}

h1, .h1 { font-size: $font-size-xxlarge; letter-spacing: $letter-spacing-tight; }
h2, .h2 { font-size: $font-size-xlarge; }
h3, .h3 { font-size: $font-size-large; }
h4, .h4 { font-size: $font-size-medium; }
h5, .h5 { font-size: $font-size-base; }
h6, .h6 { font-size: $font-size-small; text-transform: uppercase; letter-spacing: $letter-spacing-wide; }

code, pre, .code {
  font-family: $font-family-monospace;
  font-size: $font-size-small;
}

/* Element UI 组件主题变量覆盖 */
.theme-rufit {
  /* 按钮样式 */
  .el-button--primary, .el-button--success {
    background-color: #0d47a1;
    border-color: #0d47a1;
    font-family: $font-family-primary;
    font-weight: $font-weight-medium;
    letter-spacing: 0.02em;
    &:hover, &:focus {
      background-color: rgba(13, 71, 161, 0.8);
      border-color: rgba(13, 71, 161, 0.8);
    }
    &:active {
      background-color: rgba(13, 71, 161, 0.9);
      border-color: rgba(13, 71, 161, 0.9);
    }
  }

  /* 所有按钮通用样式增强 */
  .el-button {
    font-family: $font-family-primary;
    font-weight: $font-weight-medium;
    letter-spacing: 0.02em;
    padding: 8px 16px;
    border-radius: 4px;
    transition: all 0.3s ease;

    &--small {
      padding: 7px 14px;
      font-size: 14px;
    }

    &--mini {
      padding: 4px 10px;
      font-size: 12px;
    }
  }

  /* 输入框、选择器、日期选择器等表单元素 */
  .el-input__inner,
  .el-textarea__inner {
    font-family: $font-family-secondary;
    border: none;
    border-bottom: 1px solid #e4e7ed;
    padding: 8px 12px;
    height: 36px;

    &:hover {
      border-bottom-color: #cdd5df;
    }

    &:focus {
      border: 2px solid #0d47a1;
      box-shadow: 0 0 0 3px rgba(13, 71, 161, 0.15);
    }
  }

  /* 输入框图标间距修复 */
  .el-input {
    .el-input__prefix {
      left: 8px;

      .el-input__icon {
        padding-right: 5px;
      }
    }

    .el-input__suffix {
      right: 8px;
    }

    &--prefix .el-input__inner {
      padding-left: 30px;
    }

    &--suffix .el-input__inner {
      padding-right: 30px;
    }
  }

  /* 选择器缩小样式 */
  .el-select .el-input {
    .el-input__suffix {
      right: 5px;
    }
  }

  /* 选择器多选时的标签样式 */
  .el-select__tags {
    .el-tag {
      background-color: rgba(13, 71, 161, 0.1);
      color: #0d47a1;
      border-color: transparent;

      .el-tag__close {
        background-color: transparent;
        color: #0d47a1;

        &:hover {
          background-color: rgba(13, 71, 161, 0.2);
        }
      }
    }
  }

  /* 下拉菜单项尺寸调整 */
  .el-select-dropdown__item {
    padding: 0 12px;
    height: 32px;
    line-height: 32px;
  }

  /* 选择器聚焦样式 */
  .el-select .el-input.is-focus .el-input__inner,
  .el-date-editor.is-active .el-input__inner {
    border: 2px solid #0d47a1;
    box-shadow: 0 0 0 3px rgba(13, 71, 161, 0.15);
  }

  /* 成功状态 */
  .el-input.is-success .el-input__inner,
  .el-textarea.is-success .el-textarea__inner {
    border: 2px solid #10b981;
  }

  /* 错误状态 */
  .el-input.is-error .el-input__inner,
  .el-textarea.is-error .el-textarea__inner {
    border: 2px solid #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.15);
  }

  /* 禁用状态 */
  .el-input.is-disabled .el-input__inner,
  .el-textarea.is-disabled .el-textarea__inner {
    border: none;
    border-bottom: 1px solid #e4e7ed;
    background-color: #f8fafc;
  }

  /* 表格、标签页等 */
  .el-tabs__active-bar {
    background-color: #0d47a1;
    height: 2px;
    border-radius: 2px;
    bottom: 0;
  }

  .el-tabs__item {
    font-family: $font-family-primary;
    font-weight: $font-weight-medium;
    color: #606266;
    padding: 0 32px 0 12px !important;
    position: relative !important;
    transition: all 0.25s ease;

    &:hover {
      color: #0d47a1;
    }

    &.is-active {
      color: #0d47a1;
      font-weight: $font-weight-semibold;
    }

    /* 全局覆盖Element UI标签页关闭按钮样式 */
    .el-icon-close {
      width: 16px !important;
      height: 16px !important;
      position: absolute !important;
      right: 10px !important;
      top: 9px !important;
      transform: none !important;
      margin: 0 !important;
      background-color: rgba(0, 0, 0, 0.05) !important;
      border-radius: 50% !important;
      text-align: center !important;
      line-height: 16px !important;
      transition: background-color 0.2s, color 0.2s !important;

      &:hover {
        background-color: rgba(0, 0, 0, 0.15) !important;
        color: #333 !important;
      }
    }
  }

  /* 卡片式标签页样式 */
  .el-tabs--card {
    > .el-tabs__header {
      border-bottom: 1px solid #e4e7ed;
      margin-bottom: 15px;

      .el-tabs__nav {
        border: none;
      }

      .el-tabs__item {
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        margin-right: 4px;
        background-color: transparent;
        height: 32px;
        line-height: 32px;
        color: #606266;
        padding: 0 32px 0 12px !important;
        position: relative !important;

        &:hover {
          color: #0d47a1;
          border-color: #c0c4cc;
        }

        &.is-active {
          color: #0d47a1;
          background-color: rgba(13, 71, 161, 0.05);
          border-color: #0d47a1;
        }
      }
    }
  }

  /* 边框卡片式标签页样式 */
  .el-tabs--border-card {
    background: #fff;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

    > .el-tabs__header {
      background-color: #f8fafc;
      border-bottom: 1px solid #e4e7ed;
      margin: 0;

      .el-tabs__item {
        border: none;
        margin: 0 4px;
        height: 32px;
        line-height: 32px;
        color: #606266;
        padding: 0 32px 0 12px !important;
        position: relative !important;

        &:hover {
          color: #0d47a1;
        }

        &.is-active {
          color: #0d47a1;
          font-weight: $font-weight-semibold;
          background-color: #fff;
        }
      }
    }
  }

  /* 标签页样式优化 */
  .d2-multiple-page-control-group {
    position: relative;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding-right: 0;
    background-color: #f5f7fa;
    border-bottom: 1px solid #e4e7ed;
    padding: 4px 10px;

    /* 强制覆盖标签页边框样式 */
    .el-tabs--card>.el-tabs__header .el-tabs__nav,
    .el-tabs--card>.el-tabs__header .el-tabs__item,
    .el-tabs--card>.el-tabs__header {
      border: none !important;
    }

    .d2-multiple-page-control-content {
      flex: 1;
      overflow: hidden;
      position: relative;
      margin-right: 0;
    }

    .d2-multiple-page-control {
      .el-tabs__nav-wrap {
        margin-bottom: 0;
      }

      .el-tabs__header {
        margin-bottom: 0;
        border-bottom: none;
      }

      .el-tabs__item {
        height: 32px;
        line-height: 32px;
        padding: 0 32px 0 12px !important;
        position: relative !important;
        color: #606266;
        font-weight: $font-weight-medium;
        background-color: transparent;
        border: 1px solid #dcdfe6 !important;
        margin-right: 6px;
        margin-bottom: 0;
        border-radius: 4px;

        &:hover {
          color: #0d47a1;
          border-color: #c0c4cc !important;
        }

        &.is-active {
          color: #0d47a1;
          font-weight: $font-weight-semibold;
          background-color: rgba(13, 71, 161, 0.05);
          border: 1px solid #0d47a1 !important;
        }
      }

      .el-tabs__nav-next,
      .el-tabs__nav-prev {
        line-height: 32px;
        height: 32px;
        font-size: 16px;
        color: #606266;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .el-tabs__nav {
        position: relative;
        white-space: nowrap;
        border: none;
      }
    }

    .d2-multiple-page-control-btn {
      height: 32px;
      margin-left: 5px;
      position: relative;
      display: flex;
      align-items: center;
      z-index: 1;

      .el-dropdown {
        margin-right: 10px;
        .el-button-group {
          .el-button {
            height: 32px;
            border-bottom: none;
            padding: 0 8px;
            min-width: 32px;
            border-color: #dcdfe6;
            color: #606266;

            &:hover {
              color: #0d47a1;
              border-color: #c0c4cc;
            }

            i.d2-icon {
              font-size: 14px;
            }
          }
        }

        .el-dropdown-menu {
          .el-dropdown-item {
            padding: 8px 12px;

            i {
              font-size: 14px;
            }
          }
        }
      }
    }
  }

  /* 表格样式 */
  .el-table {
    th {
      background-color: #f5f7fa;
      font-family: $font-family-primary;
      font-weight: $font-weight-medium;
      color: #606266;
      padding: 12px 0;
      border-bottom: 1px solid #ebeef5;
    }

    td {
      padding: 12px 0;
      border-bottom: 1px solid #ebeef5;

      .el-button--text {
        color: #0d47a1;

        &:hover, &:focus {
          color: rgba(13, 71, 161, 0.8);
        }
      }
    }

    tr {
      transition: all 0.3s ease;

      &:hover > td {
        background-color: #f5f7fa;
      }

      &.el-table__row--striped > td {
        background-color: #fafafa;
      }

      &.current-row > td {
        background-color: rgba(16, 185, 129, 0.1);
      }
    }

    /* 修复表格列对齐问题，确保align属性生效 */
    .is-center .cell {
      justify-content: center;
    }

    .is-right .cell {
      justify-content: flex-end;
    }

    .is-left .cell {
      justify-content: flex-start;
    }
  }

  /* 标签样式优化 - 使用el-tag及其类型 */
  .el-tag {
    font-family: $font-family-primary;
    font-weight: $font-weight-normal;
    border-radius: 12px;
    padding: 0 12px;
    height: 24px;
    line-height: 22px;
    font-size: 12px;
    border-width: 0;
    letter-spacing: 0.02em;

    /* 标签各类型样式 */
    &--success {
      background-color: rgba(16, 185, 129, 0.1);
      border-color: transparent;
      color: #10b981;
    }

    &--info {
      background-color: rgba(96, 165, 250, 0.1);
      border-color: transparent;
      color: #3b82f6;
    }

    &--warning {
      background-color: rgba(245, 158, 11, 0.1);
      border-color: transparent;
      color: #f59e0b;
    }

    &--danger {
      background-color: rgba(239, 68, 68, 0.1);
      border-color: transparent;
      color: #ef4444;
    }

    /* 设置标签中的关闭按钮样式 */
    .el-tag__close {
      color: inherit;
      opacity: 0.7;

      &:hover {
        background-color: transparent;
        color: inherit;
        opacity: 1;
      }
    }
  }

  .el-checkbox__input.is-checked .el-checkbox__inner,
  .el-radio__input.is-checked .el-radio__inner,
  .el-switch.is-checked .el-switch__core {
    border-color: #0d47a1;
    background-color: #0d47a1;
  }

  /* 分页、标签等 */
  .el-pagination {
    font-family: $font-family-secondary;

    .btn-prev, .btn-next {
      font-family: $font-family-primary;
    }
  }

  .el-pagination__sizes .el-input .el-input__inner:hover,
  .el-pagination__jump .el-input .el-input__inner:hover,
  .el-tag--primary {
    border-color: #0d47a1;
  }

  .el-pagination button:hover,
  .el-pagination__sizes .el-input .el-input__inner:hover,
  .el-select-dropdown__item.selected {
    color: #0d47a1;
  }

  /* 滑块、进度条等 */
  .el-slider__bar,
  .el-progress-bar__inner {
    background-color: #0d47a1;
  }

  /* 对话框、表单等 */
  .el-dialog__title,
  .el-form-item__label {
    font-family: $font-family-primary;
    font-weight: $font-weight-medium;
  }

  /* 对话框圆角优化 */
  .el-dialog {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);

    .el-dialog__header {
      padding: 18px 20px;
    }

    .el-dialog__body {
      padding: 20px;
    }

    .el-dialog__footer {
      padding: 16px 20px;
    }
  }

  /* 菜单 */
  .el-menu-item,
  .el-submenu__title {
    font-family: $font-family-primary;
    font-weight: $font-weight-normal;
    letter-spacing: 0.02em;
  }



  /* 顶栏菜单滚动控件美化 */
  .d2-theme-header-menu {
    .d2-theme-header-menu__prev,
    .d2-theme-header-menu__next {
      border-radius: 4px;
      transition: all 0.25s ease;

      &:hover {
        background-color: rgba(13, 71, 161, 0.05);
      }
    }
  }

  /* 顶栏菜单样式 */
  .d2-theme-header {
    .el-menu {
      .el-menu-item, .el-submenu__title {
        color: #000000;
        i {
          color: #000000;
        }
        .el-submenu__icon-arrow {
          color: #000000;
        }
      }
    }
  }

  /* 侧边栏菜单样式 */
  .d2-layout-header-aside-menu-side {
    .el-menu {
      background-color: #ffffff;

      .el-menu-item {
        height: 42px;
        line-height: 42px;
        font-family: $font-family-primary;
        font-weight: $font-weight-normal;
        font-size: 13px;
        color: #000000;
        padding: 0 16px;
        border-radius: 6px;
        margin: 3px 6px;
        transition: all 0.25s ease;

        i {
          color: #000000;
          transition: all 0.25s ease;
        }

        &:hover {
          color: #0d47a1;
          background-color: rgba(13, 71, 161, 0.05);

          i {
            color: #0d47a1;
          }
        }

        &.is-active {
          color: #ffffff;
          font-weight: $font-weight-medium;
          background-color: #0d47a1;
          box-shadow: 0 2px 6px rgba(13, 71, 161, 0.2);

          i {
            color: #ffffff;
          }
        }
      }

      .el-submenu {
        .el-submenu__title {
          height: 42px;
          line-height: 42px;
          font-family: $font-family-primary;
          font-weight: $font-weight-normal;
          font-size: 13px;
          color: #000000;
          padding: 0 16px;
          border-radius: 6px;
          margin: 3px 6px;
          transition: all 0.25s ease;

          i {
            color: #000000;
            transition: all 0.25s ease;
          }

          .el-submenu__icon-arrow {
            margin-top: -4px;
            font-size: 12px;
            color: #000000;
            transition: all 0.25s ease;
          }

          &:hover {
            color: #0d47a1;
            background-color: rgba(13, 71, 161, 0.05);

            i {
              color: #0d47a1;
            }

            .el-submenu__icon-arrow {
              color: #0d47a1;
            }
          }
        }

        &.is-active > .el-submenu__title {
          color: #0d47a1;
          font-weight: $font-weight-medium;

          i {
            color: #0d47a1;
          }

          .el-submenu__icon-arrow {
            color: #0d47a1;
          }
        }

        .el-menu {
          background-color: transparent;
          padding-left: 8px;
        }
      }
    }

    /* 折叠状态下的侧边栏 */
    .el-menu--collapse {
      .el-menu-item, .el-submenu__title {
        padding: 0 14px !important;
        margin: 3px auto;
        width: calc(100% - 12px);
      }
    }
  }



  /* 面包屑导航 */
  .el-breadcrumb__item {
    font-family: $font-family-primary;

    &__inner.is-link {
      font-weight: $font-weight-normal;
    }

    &:last-child .el-breadcrumb__inner {
      font-weight: $font-weight-semibold;
    }
  }
}
