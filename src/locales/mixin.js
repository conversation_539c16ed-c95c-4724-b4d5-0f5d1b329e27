export default {
  methods: {
    onChangeLocale (command) {
      this.$i18n.locale = command

      // 获取当前语言的名称
      const langName = this.languages ?
        (this.languages.find(lang => lang.value === command) || {}).label :
        this.$t('_name');

      let message = `${this.$t('language.current')}：${langName} [ ${this.$i18n.locale} ]`
      if (process.env.VUE_APP_BUILD_MODE === 'PREVIEW') {
        message = [
          `${this.$t('language.current')}：${langName} [ ${this.$i18n.locale} ]`,
          '没有配置具体的语言数据'
        ].join('<br/>')
      }
      this.$message.success({
        dangerouslyUseHTMLString: true,
        message,
        duration: 3000
      })
      // this.$notify({
      //   title: this.$t('language.change'),
      //   dangerouslyUseHTMLString: true,
      //   message,
      //   duration: 3000
      // })
    }
  }
}
