{"_element": "en", "_name": "English", "language": {"current": "Current Language", "change": "Language Changed"}, "wechat": {"account": {"wechatNumber": "<PERSON><PERSON> Account", "loginStatus": "Login Status", "search": "Search", "login": "Account <PERSON>gin", "syncStatus": "Sync Login Status", "refresh": "Refresh", "avatar": "Avatar", "nickname": "Nickname", "signature": "Signature", "createTime": "Created Time", "selectOne": "Please select one account to login", "loginConfirm": "If this is not your first time using the system, please select a previously logged-in account from the table below to login.<br> If you are logging in for the first time, please note that your account will normally disconnect once randomly within 48 hours after login. Please pay attention to your account login status.", "tip": "Tip", "confirm": "Confirm", "cancel": "Cancel", "syncCompleted": "Status sync completed", "initializing": "Initializing contacts", "status": {"online": "Online", "offline": "Offline"}, "loginDialog": {"title": "Account <PERSON>gin", "subtitle": "Choose your login region, then scan the QR code to complete login", "steps": {"selectRegion": "Select Region", "scanQrcode": "Scan QR Code", "loginSuccess": "Login Success"}, "region": {"title": "Select Your Login Region", "subtitle": "For example, if your scanning device is in Shanghai, please select Shanghai. If the scanning device is in Beijing, please select Beijing", "placeholder": "Please select your region", "generateBtn": "Generate Login QR Code"}, "qrcode": {"title": "Scan QR Code to Login", "subtitle": "Scan the QR code below with your phone and click confirm until completion", "loading": "QR code is loading, please wait...", "countdown": "Valid for", "seconds": "seconds"}, "success": {"title": "Login Successful!", "subtitle": "You have successfully logged in to {region}, and can start using the system now", "status": "Login Status: Connected | Region: {region}", "startBtn": "Start Using"}, "initializing": {"title": "Verifying Login Information", "subtitle": "Will redirect automatically after verification", "progress": "Verification Progress:"}, "errors": {"selectRegion": "Please select a login region first", "qrcodeFailed": "Failed to generate QR code, please try again", "loginFailed": "<PERSON><PERSON> failed, please try again", "initFailed": "Contact initialization failed, please try again", "qrcodeExpired": "QR code has expired, please regenerate"}, "unknownRegion": "Unknown Region"}}, "address": {"friend": "Friend Account", "nickname": "Friend Nickname", "type": "Type", "search": "Search", "batchMessage": "Mass Messaging", "refresh": "Refresh", "avatarColumn": "Friend/Group Avatar", "wcIdColumn": "wcId", "accountColumn": "Account", "friendWcIdColumn": "Friend/Group wcId", "friendAccountColumn": "Friend Account", "nicknameColumn": "Friend/Group Nickname", "labelColumn": "Label", "typeColumn": "Address Type", "genderColumn": "Gender", "createTimeColumn": "Create Time", "dialog": {"title": "Mass Messaging", "messageType": "Message Type", "textMessage": "Text Message", "fileMessage": "File Message", "inputPlaceholder": "Please enter content", "send": "Send Message", "selectFile": "Select File", "fileTip": "Only 3 files can be uploaded, not exceeding 3MB. Note that filenames should not contain special characters."}, "message": {"selectFriend": "Please select friends to send the message to", "sendSuccess": "Message sent successfully", "fileCountLimit": "You can upload at most 3 files", "fileSizeLimit": "File size cannot exceed 3MB"}, "typeOptions": {"friend": "Friend", "group": "Group"}, "genderOptions": {"other": "Other", "male": "Male", "female": "Female"}}}, "menu": {"systemManager": "System", "menuManager": "<PERSON><PERSON>", "deptManager": "Client", "roleManager": "User Permission Assignment", "postManger": "Post", "userManager": "User", "dictManager": "Dictionary", "scheduleManager": "Task Scheduling", "monitorManager": "System Monitoring", "onlineUser": "Online Users", "operLogManager": "Operation Log", "loginLogManager": "<PERSON><PERSON>g", "messageManager": "Message", "mailManager": "Mail Settings", "wechatManager": "Account & Services", "accountManget": "Account <PERSON>gin", "addressManget": "Mass Messaging", "metricsManager": "Metrics Monitoring", "customerSessionMetrics": "Customer Session Metrics", "dashboard": "Dashboard", "followUpConfig": "Follow-up Strategy", "followUpManager": "Active Follow-up", "appManager": "Application ", "AppInfoManager": "Application Information", "appItemManager": "Configuration Information", "knowledgeManager": "Knowledge Base ", "qaManager": "QA", "documentManager": "Document", "user-setting": "User Settings", "index": "Home"}, "login": {"slogan": "Welcome to MarsMind, the result-driven Agent system.", "loginTab": "<PERSON><PERSON>", "registerTab": "Register", "username": "Username", "password": "Password", "confirmPassword": "Confirm Password", "inviteCode": "Invitation Code", "loginButton": "<PERSON><PERSON>", "registerButton": "Register", "agreementPrefix": "I agree to the", "termOfService": "Terms of Service", "privacyPolicy": "Privacy Policy", "agreementConnector": " and", "dataUsageAgreement": "Data Usage Policy", "agreementTips": "Please read and understand all terms carefully", "closeButton": "Close", "errorUsername": "Please enter username", "errorPassword": "Please enter password", "errorConfirmPassword": "Please confirm your password", "errorPasswordMatch": "Passwords do not match", "errorInviteCode": "Please enter invitation code", "errorAgreement": "Please read and agree to the terms", "registerSuccess": "Registration successful, logging in...", "autoLoginFailed": "Auto login failed, please login manually"}, "knowledge": {"qa": {"title": "QA Management", "subtitle": "Smart Q&A Knowledge Base", "search": {"placeholder": "Enter keywords to search QA content...", "button": "Search", "deptPlaceholder": "Select Customer"}, "actions": {"add": "Add", "batch": "<PERSON><PERSON>", "edit": "Edit", "delete": "Delete"}, "stats": {"total": "Total {total} QA records (Page {current}/{totalPages}, showing {start}-{end})"}, "filter": {"title": "Filter Source", "text": "Filtered: {source}", "allSources": "All Sources"}, "list": {"question": "Question", "answer": "Answer", "source": "Source"}, "dialog": {"add": {"title": "Add QA Record", "desc": "Fill in question and answer information to create a new QA record.", "question": "Question", "answer": "Answer", "tags": "Tags", "source": "Document Source", "newSource": "Enter new document name", "sourceToggle": {"select": "Select existing document", "create": "or create new document"}, "placeholder": {"question": "Enter question content...", "answer": "Enter answer content...", "tags": "Enter tags separated by commas (e.g. React, JavaScript, Frontend)", "source": "Please select document source"}, "tips": {"tags": "Multiple tags should be separated by commas"}, "buttons": {"cancel": "Cancel", "saveAndContinue": "Save & Continue", "add": "+ Add QA", "confirm": "Confirm Edit"}, "validation": {"question": "Please enter a question", "answer": "Please enter an answer", "source": "Please select a document source", "newSource": "Please enter a new document name"}}, "batchImport": {"title": "Batch Upload QA Records", "desc": "Import QA records by uploading an Excel file. First column for questions, second column for answers.", "format": {"title": "Excel Format Requirements", "desc": "Please prepare an Excel file with questions in the first column and answers in the second column", "download": "Download Example"}, "upload": {"label": "Select Excel File", "button": "Select File", "clear": "Clear", "status": {"none": "No file selected", "selected": "File selected: {filename}"}}, "source": {"label": "Document Source"}, "buttons": {"cancel": "Cancel", "upload": "Upload"}, "messages": {"selectFile": "Please select a file first", "selectSource": "Please select or enter a document source", "success": "Upload successful"}}, "delete": {"title": "Confirmation", "message": "Are you sure you want to delete this QA record?", "confirm": "Confirm", "cancel": "Cancel", "success": "Delete successful", "canceled": "Delete canceled"}}, "messages": {"addSuccess": "Added successfully", "editSuccess": "Edit successful"}}, "file": {"title": "File Management", "subtitle": "Upload and manage your documents, supporting PDF, Word, Excel and more formats", "root": "Root Directory", "recycleBin": "Recycle Bin", "searchPlaceholder": "Search documents...", "upload": "Upload File", "recycle": "Recycle Bin", "return": "Return", "uploading": "Uploading", "uploadingStatus": "Uploading", "uploadSuccess": "File uploaded successfully", "uploadFail": "File upload failed: {msg}", "fileSizeExceeded": "File size exceeds limit, maximum allowed is {maxSize}", "empty": "No files", "recycleEmpty": "Recycle bin is empty", "selectAll": "Select All", "deleteConfirm": "This action will move the file to the recycle bin. Continue?", "deleteSuccess": "Deleted successfully, moved to recycle bin", "restoreConfirm": "Confirm to restore this file?", "restoreSuccess": "Restored successfully", "enableSuccess": "Enabled successfully", "disableSuccess": "Disabled successfully", "actionFail": "Operation failed, please try again", "completed": "Completed", "processing": "Processing", "enabled": "Enabled", "disabled": "Disabled", "fragmentCount": "{count} fragments", "qaCount": "{count} QAs", "miningStatus": {"1": "Abnormal", "2": "Running", "3": "Completed"}, "delete": "Delete", "confirm": "Confirm", "cancel": "Cancel", "restore": "Rest<PERSON>", "noItemsSelected": "Please select files to delete", "batchDeleteConfirm": "Are you sure you want to move the selected files to the recycle bin?", "batchDeleteSuccess": "Batch delete successful, moved to recycle bin", "cancelSelection": "Cancel Selection", "selectedItems": "{count} items selected", "batchRestoreConfirm": "Are you sure you want to restore the selected files?", "batchRestoreSuccess": "Batch restore successful"}}, "header": {"user": {"hello": "Hello", "notLoggedIn": "Not Logged In", "setting": "Settings", "clearCache": "<PERSON>ache", "logout": "Logout", "logoutConfirmTitle": "Logout User", "logoutConfirmMessage": "Are you sure you want to logout the current user?", "logoutCancelled": "Logout operation cancelled", "updateSuccess": "Update Successful"}, "logo": {"title": "MarsMind", "subtitle": "Smart Business"}, "fullscreen": {"enter": "Full Screen", "exit": "Exit Full Screen"}, "size": {"default": "<PERSON><PERSON><PERSON>", "medium": "Medium", "small": "Small", "mini": "Mini", "title": "Notice", "message": "Updated <b>component</b> <b>default size</b><br/>For example button size, <b>not font size</b>"}}, "tabs": {"close": "Close", "closeLeft": "Close Left", "closeRight": "Close Right", "closeOther": "Close Others", "closeAll": "Close All", "refresh": "Refresh", "unnamed": "Unnamed"}, "sideMenu": {"empty": "No sidebar menu"}, "user": {"setting": {"userTab": "User Settings", "passwordTab": "Password Settings", "nickname": "Nickname", "phoneNumber": "Phone Number", "email": "Email", "gender": "Gender", "male": "Male", "female": "Female", "unknown": "Unknown", "update": "Update", "reset": "Reset", "oldPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "submit": "Submit", "validation": {"nicknameRequired": "Please enter a nickname", "passwordRequired": "Please enter a password", "oldPasswordRequired": "Please enter your current password", "passwordSameAsOld": "New password is the same as current password", "confirmPasswordRequired": "Please confirm your password", "passwordMismatch": "Passwords do not match!", "formValidationFailed": "Form validation failed, please check"}, "messages": {"updateSuccess": "Updated successfully"}}}, "dashboard": {"noDashboard": "No dashboard configured, please contact administrator for configuration", "contactAdmin": "No dashboard configured, please contact administrator if needed"}}