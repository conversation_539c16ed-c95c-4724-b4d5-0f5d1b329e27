<template>
  <div
    class="d2-floating-bubble"
    :style="bubbleStyle"
    @mousedown="startDrag"
    @click="handleClick"
  >
    <div class="bubble-icon">
      <i class="el-icon-chat-dot-round"></i>
    </div>
    
    <!-- 弹窗 -->
    <el-dialog
      title="智能助手"
      :visible.sync="dialogVisible"
      width="80%"
      :close-on-click-modal="false"
      custom-class="chat-dialog"
    >
      <div class="chat-container">
        <iframe
          :src="chatUrl"
          frameborder="0"
          class="chat-iframe"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'd2-floating-bubble',
  data() {
    return {
      dialogVisible: false,
      chatUrl: 'https://chat.marsmind.co',
      isDragging: false,
      startX: 0,
      startY: 0,
      position: {
        x: window.innerWidth - 80,
        y: window.innerHeight - 150
      }
    }
  },
  computed: {
    bubbleStyle() {
      return {
        left: this.position.x + 'px',
        top: this.position.y + 'px'
      }
    }
  },
  mounted() {
    document.addEventListener('mousemove', this.onDrag)
    document.addEventListener('mouseup', this.stopDrag)
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    document.removeEventListener('mousemove', this.onDrag)
    document.removeEventListener('mouseup', this.stopDrag)
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    startDrag(e) {
      this.isDragging = true
      this.startX = e.clientX - this.position.x
      this.startY = e.clientY - this.position.y
      e.preventDefault()
    },
    onDrag(e) {
      if (!this.isDragging) return
      
      const newX = e.clientX - this.startX
      const newY = e.clientY - this.startY
      
      // 边界检测
      const maxX = window.innerWidth - 60
      const maxY = window.innerHeight - 60
      
      this.position.x = Math.max(0, Math.min(newX, maxX))
      this.position.y = Math.max(0, Math.min(newY, maxY))
    },
    stopDrag() {
      this.isDragging = false
    },
    handleClick(e) {
      if (!this.isDragging) {
        this.dialogVisible = true
      }
    },
    handleResize() {
      // 窗口大小改变时调整位置
      const maxX = window.innerWidth - 60
      const maxY = window.innerHeight - 60
      
      this.position.x = Math.min(this.position.x, maxX)
      this.position.y = Math.min(this.position.y, maxY)
    }
  }
}
</script>

<style lang="scss" scoped>
.d2-floating-bubble {
  position: fixed;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  cursor: pointer;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease;
  user-select: none;

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.4);
  }

  &:active {
    transform: scale(0.95);
  }

  .bubble-icon {
    color: white;
    font-size: 24px;
  }
}

.chat-container {
  height: 70vh;
  min-height: 500px;
}

.chat-iframe {
  width: 100%;
  height: 100%;
  border: none;
}
</style>

<style lang="scss">
.chat-dialog {
  .el-dialog__body {
    padding: 0;
  }
}
</style>