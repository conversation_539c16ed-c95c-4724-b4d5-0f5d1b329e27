<template>
  <el-dropdown placement="bottom" size="small" @command="onChangeLocale">
    <el-button class="d2-mr btn-text can-hover" type="text">
      <d2-icon name="language" style="font-size: 16px;"/>
    </el-button>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item
        v-for="language in languages"
        :key="language.value"
        :command="language.value">
        <d2-icon :name="$i18n.locale === language.value ? 'dot-circle-o' : 'circle-o'" class="d2-mr-5"/>
        <span class="flag-emoji">{{ language.flag }}</span>
        {{ language.label }}
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
import localeMixin from '@/locales/mixin.js'
export default {
  mixins: [
    localeMixin
  ],
  data() {
    return {
      languages: [
        {
          value: 'en',
          label: 'English',
          flag: '🇺🇸'
        },
        {
          value: 'zh-chs',
          label: '中文',
          flag: '🇨🇳'
        },
        {
          value: 'ja',
          label: '日本語',
          flag: '🇯🇵'
        }
      ]
    }
  },
  computed: {
    currentLanguage() {
      const locale = this.$i18n ? this.$i18n.locale : 'en';
      return this.languages.find(lang => lang.value === locale) || this.languages[0];
    }
  }
}
</script>

<style lang="scss" scoped>
.flag-emoji {
  font-size: 16px;
  line-height: 1;
  margin-right: 5px;
}
</style>
