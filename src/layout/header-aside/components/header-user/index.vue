<template>
  <el-dropdown size="small" class="d2-mr">
    <span class="btn-text">{{info.name ? `${$t('header.user.hello')} ${info.name}` : $t('header.user.notLoggedIn')}}</span>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item @click.native="setting">
        <d2-icon name="cog" class="d2-mr-5" />{{ $t('header.user.setting') }}
      </el-dropdown-item>
      <el-dropdown-item @click.native="updateVueCache">
        <i class="fa fa-trash" aria-hidden="true"></i>
        {{ $t('header.user.clearCache') }}
      </el-dropdown-item>
      <el-dropdown-item @click.native="logOff">
        <d2-icon name="power-off" class="d2-mr-5" />{{ $t('header.user.logout') }}
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
import { mapState, mapActions } from "vuex";
import router from "@/router";
export default {
  computed: {
    ...mapState("d2admin/user", ["info"]),
  },
  methods: {
    ...mapActions("d2admin/account", ["logout", "updateCache"]),
    /**
     * @description 登出
     */
    logOff() {
      this.logout({
        confirm: true,
      });
    },
    /**
     * 设置打开页签
     */
    setting() {
      router.push({
        name: "user-setting",
      });
    },
    /**
     * 更新缓存
     */
    updateVueCache() {
      let _self = this;
      _self
        .updateCache({ to: this.$route.fullPath || "/" })
        .then((result) => {
          _self.$message.success(_self.$t('header.user.updateSuccess'));
        });
    },
  },
};
</script>
