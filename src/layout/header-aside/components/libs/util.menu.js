/**
 * @description 创建菜单
 * @param {Function} h createElement
 * @param {Object} menu 菜单项
 */
export function elMenuItem (h, menu) {
  let icon = null
  if (menu.icon) icon = <i class={ `${menu.icon}` }/>
  else if (menu.iconSvg) icon = <d2-icon-svg name={ menu.iconSvg }/>
  else icon = <i class="fa fa-file-o"/>
  
  // 获取菜单翻译
  let title = menu.title || '未命名菜单'
  
  // 如果有对应的路由标识，尝试获取翻译
  if (menu.name) {
    const i18nKey = `menu.${menu.name}`
    if (this.$i18n && this.$i18n.te(i18nKey)) {
      title = this.$i18n.t(i18nKey) || menu.title
    }
  }
  
  return <el-menu-item
    key={ menu.path }
    index={ menu.path }>
    { icon }
    <span slot="title">{ title }</span>
  </el-menu-item>
}

/**
 * @description 创建子菜单
 * @param {Function} h createElement
 * @param {Object} menu 菜单项
 */
export function elSubmenu (h, menu) {
  let icon = null
  if (menu.icon) icon = <i slot="title" class={ `${menu.icon}` }/>
  else if (menu.iconSvg) icon = <d2-icon-svg slot="title" name={ menu.iconSvg }/>
  else icon = <i slot="title" class="fa fa-folder-o"/>
  
  // 获取菜单翻译
  let title = menu.title || '未命名菜单'
  
  // 如果有对应的路由标识，尝试获取翻译
  if (menu.name) {
    const i18nKey = `menu.${menu.name}`
    if (this.$i18n && this.$i18n.te(i18nKey)) {
      title = this.$i18n.t(i18nKey) || menu.title
    }
  }
  
  return <el-submenu
    key={ menu.path }
    index={ menu.path }>
    { icon }
    <span slot="title">{ title }</span>
    { menu.children.map(child => createMenu.call(this, h, child)) }
  </el-submenu>
}

/**
 * @description 在组件中调用此方法渲染菜单项目
 * @param {Function} h createElement
 * @param {Object} menu 菜单项
 */
export function createMenu (h, menu) {
  if (menu.children === undefined) return elMenuItem.call(this, h, menu)
  return elSubmenu.call(this, h, menu)
}
