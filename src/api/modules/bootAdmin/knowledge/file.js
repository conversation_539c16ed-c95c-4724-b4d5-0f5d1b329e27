import {contextPath, knowledge} from "@/api/baseServer"
import {
  knowledgeFileDeleteByIdUrl,
  knowledgeFileListPageUrl,
  knowledgeFileSaveUrl,
  knowledgeFileUpdateUrl,
  knowledgeFileBatchUpdateUrl
} from "@/api/baseUrl"

export default ({https}) => ({
  fileListPage(data) {
    let url = baseUrl(knowledgeFileListPageUrl)
    return https.httpPost({url: url, data: data});
  },
  fileSave(data) {
    let url = baseUrl(knowledgeFileSaveUrl)
    return https.httpPost({url: url, data: data});
  },
  fileUpdate(id,data) {
    let url = baseUrl(knowledgeFileUpdateUrl + '/' + id);
    return https.httpPost({url: url, data: data});
  },
  fileBatchUpdate(data) {
    let url = baseUrl(knowledgeFileBatchUpdateUrl);
    return https.httpPost({url: url, data: data});
  },
  fileDelete(id) {
    let url = baseUrl(knowledgeFileDeleteByIdUrl + '/' + id);
    return https.httpGet({url: url, params: null});
  },
})

function baseUrl(url) {
  if (url) {
    url = contextPath + knowledge + url
  } else {
    url = contextPath + knowledge
  }
  return url
}
