import {contextPath, knowledge} from "@/api/baseServer"
import {
  knowledgeQaDeleteUrl,
  knowledgeQaFileNameListUrl,
  knowledgeQaListPageUrl,
  knowledgeQaSaveUrl,
  knowledgeQaTemplateDownloadUrl,
  knowledgeQaUploadUrl
} from "@/api/baseUrl"

export default ({https}) => ({
  qaListPage(data) {
    let url = baseUrl(knowledgeQaListPageUrl)
    return https.httpPost({url: url, data: data});
  },
  qaSave(data) {
    let url = baseUrl(knowledgeQaSaveUrl)
    return https.httpPost({url: url, data: data});
  },
  getFileName(data) {
    let url = baseUrl(knowledgeQaFileNameListUrl);
    return https.httpGet({url: url, params: data});
  },
  deleteQa(data) {
    let url = baseUrl(knowledgeQaDeleteUrl);
    return https.httpPost({url: url, data: data});
  },
  templateDownload() {
    let url = baseUrl(knowledgeQaTemplateDownloadUrl);
    return https.httpExport({url: url});
  },
  uploadQa(data, headers) {
    let url = baseUrl(knowledgeQaUploadUrl);
    return https.httpPostExt({url: url, data: data, headers: headers});
  }
})

function baseUrl(url) {
  if (url) {
    url = contextPath + knowledge + url
  } else {
    url = contextPath + knowledge
  }
  return url
}
