import {contextPath, oss} from "@/api/baseServer"
import {ossFileUploadUrl} from "@/api/baseUrl"

export default ({https}) => ({
  ossFileUpload(data, headers) {
    let url = baseUrl(ossFileUploadUrl);
    return https.httpPostExt({url: url, data: data, headers: headers});
  }
})

function baseUrl(url) {
  if (url) {
    url = contextPath + oss + url
  } else {
    url = contextPath + oss
  }
  return url
}
