import {contextPath, wechatServer} from "@/api/baseServer";
import {
  syncWktWechatLoginStatusUrl,
  wechatAccountGetListUrl,
  wechatAccountGetLoginInfoUrl,
  wechatAccountLoginUrl,
  wechatAccountListPageUrl,
  wechatAddressInitUrl,
  wechatAddressListPageUrl,
  wechatLabelGetListUrl,
  wechatMessageSendFileUrl,
  wechatMessageSendTextUrl
} from "@/api/baseUrl";


export default ({https}) => ({
  /**
   * 分页查询
   * @param {jsonString} data
   */
  wechatAccountListPage(data) {
    let url = baseUrl(wechatAccountListPageUrl);
    return https.httpPost({url: url, data: data});
  },
  wechatAccountLogin(data) {
    let url = baseUrl(wechatAccountLoginUrl);
    return https.httpGet({url: url, params: data});
  },
  wechatAccountList() {
    let url = baseUrl(wechatAccountGetListUrl);
    return https.httpGet({url: url, params: null});
  },
  syncWktWechatLoginStatus() {
    let url = baseUrl(syncWktWechatLoginStatusUrl);
    return https.httpGet({url: url, params: null});
  },
  wechatAccountGetLoginInfo(data) {
    let url = baseUrl(wechatAccountGetLoginInfoUrl);
    return https.httpPost({url: url, data: data});
  },

  wechatAddressListPage(data) {
    let url = baseUrl(wechatAddressListPageUrl);
    return https.httpPost({url: url, data: data});
  },
  wechatAddressInit(data) {
    let url = baseUrl(wechatAddressInitUrl);
    return https.httpPost({url: url, data: data});
  },
  wechatMessageSendText(data) {
    let url = baseUrl(wechatMessageSendTextUrl);
    return https.httpPost({url: url, data: data});
  },
  wechatMessageSendFile(data, headers) {
    let url = baseUrl(wechatMessageSendFileUrl);
    return https.httpPostExt({url: url, data: data, headers: headers});
  },

  wechatLabelList() {
    let url = baseUrl(wechatLabelGetListUrl);
    return https.httpGet({url: url, params: null});
  },

})


function baseUrl(url) {
  if (url) {
    url = contextPath + wechatServer + url
  } else {
    url = contextPath + wechatServer
  }
  return url
}
