import {contextPath, metricsServer} from "@/api/baseServer";
import {
  customerSessionListPageUrl
} from "@/api/baseUrl";

export default ({https}) => ({

  /**
   * 分页查询
   * @param {jsonString} data
   */
  customerSessionListPage(data) {
    let url = baseUrl(customerSessionListPageUrl);
    return https.httpPost({url: url, data: data});
  },

})


function baseUrl(url) {
  if (url) {
    url = contextPath + metricsServer + url
  } else {
    url = contextPath + metricsServer
  }
  return url
}
