import {app, contextPath} from "@/api/baseServer";
import {
  appInfoListPageUrl,
  appInfoListUrl,
  appInfoSaveUrl,
  appInfoUpdateByIdUrl,
  getPortUrl,
  startAppUrl,
  stopAppUrl
} from "@/api/baseUrl";

export default ({https}) => ({

  /**
   * 分页查询
   * @param {jsonString} data
   */
  appInfoListPage(data) {
    let url = baseUrl(appInfoListPageUrl);
    return https.httpPost({url: url, data: data});
  },
  appInfoSave(data) {
    let url = baseUrl(appInfoSaveUrl);
    return https.httpPost({url: url, data: data});
  },
  appInfoUpdateById(id, data) {
    let url = baseUrl(appInfoUpdateByIdUrl + '/' + id);
    return https.httpPost({url: url, data: data});
  },
  getPort(data) {
    let url = baseUrl(getPortUrl);
    return https.httpGet({url: url, params: data});
  },
  appInfoList() {
    let url = baseUrl(appInfoListUrl);
    return https.httpGet({url: url, params: null});
  },
  appInfoStart(data) {
    let url = baseUrl(startAppUrl);
    return https.httpGet({url: url, params: data});
  },
  appInfoStop(data) {
    let url = baseUrl(stopAppUrl);
    return https.httpGet({url: url, params: data});
  }

})


function baseUrl(url) {
  if (url) {
    url = contextPath + app + url
  } else {
    url = contextPath + app
  }
  return url
}
