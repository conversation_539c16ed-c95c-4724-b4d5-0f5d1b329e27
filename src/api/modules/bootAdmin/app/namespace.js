import {app, contextPath} from "@/api/baseServer";
import {
  appNamespaceDeleteByIdUrl,
  appNamespaceListPageUrl,
  appNamespaceSaveUrl,
  appNamespaceUpdateByIdUrl
} from "@/api/baseUrl";

export default ({https}) => ({

  appNamespaceListPage(data) {
    let url = baseUrl(appNamespaceListPageUrl);
    return https.httpPost({url: url, data: data});
  },
  appNamespaceSave(data) {
    let url = baseUrl(appNamespaceSaveUrl);
    return https.httpPost({url: url, data: data});
  },
  appNamespaceUpdateById(id, data) {
    let url = baseUrl(appNamespaceUpdateByIdUrl + '/' + id);
    return https.httpPost({url: url, data: data});
  },
  appNamespaceDeleteById(id) {
    let url = baseUrl(appNamespaceDeleteByIdUrl + '/' + id);
    return https.httpGet({url: url, params: null});
  }

})


function baseUrl(url) {
  if (url) {
    url = contextPath + app + url
  } else {
    url = contextPath + app
  }
  return url
}
