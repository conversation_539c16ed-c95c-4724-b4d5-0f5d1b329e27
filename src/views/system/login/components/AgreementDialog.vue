<template>
  <el-dialog
    :visible.sync="visible"
    width="480px"
    class="agreement-dialog"
    :before-close="handleClose"
    append-to-body
    center
    :show-close="false"
  >
    <div class="agreement-header">
      <span class="close-btn" @click="handleClose">×</span>
      <h2 class="agreement-title">{{ agreementData.head }}</h2>
      <p class="agreement-desc">{{ agreementData.headDesc }}</p>
      <p class="agreement-tips">{{ $t('login.agreementTips') }}</p>
    </div>
    <div class="agreement-content">
      <div v-for="(section, index) in agreementData.body" :key="index" class="agreement-section">
        <h3 class="section-title">{{ section.head }}</h3>
        <p class="section-content" v-html="formatContent(section.body)"></p>
      </div>
      <div class="agreement-bottom">
        <p v-html="formatContent(agreementData.bottom)"></p>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleClose">{{ $t('login.closeButton') }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
import agreementData from './agreement.json';

export default {
  name: 'AgreementDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    agreementType: {
      type: String,
      default: 'TermOfService'
    }
  },
  data() {
    return {
      agreementData: {
        head: '',
        headDesc: '',
        body: [],
        bottom: ''
      }
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.loadAgreementData();
      }
    },
    agreementType() {
      if (this.visible) {
        this.loadAgreementData();
      }
    },
    '$i18n.locale'() {
      // 当语言切换时重新加载协议内容
      if (this.visible) {
        this.loadAgreementData();
      }
    }
  },
  mounted() {
    this.loadAgreementData();
  },
  methods: {
    loadAgreementData() {
      // 根据当前语言选择协议内容
      const locale = this.$i18n ? this.$i18n.locale : 'zh-chs';
      let currentLang = 'zh-chs';
      
      // 如果没有对应的语言，默认使用中文
      if (agreementData[locale]) {
        currentLang = locale;
      }
      
      let data = {};
      
      if (this.agreementType === 'TermOfService') {
        data = {...agreementData[currentLang].TermOfService};
      } else if (this.agreementType === 'privacyPolicy') {
        data = {...agreementData[currentLang].privacyPolicy};
      } else if (this.agreementType === 'dataUsageAgreement') {
        data = {...agreementData[currentLang].dataUsageAgreement};
      }
      
      // 添加通用底部内容
      data.bottom = agreementData[currentLang].bottom;
      this.agreementData = data;
    },
    formatContent(content) {
      // 将\n转换为<br>标签
      if (!content) return ''; // 添加空值检查
      return content.replace(/\n/g, '<br>');
    },
    handleClose() {
      this.$emit('update:visible', false);
    }
  }
};
</script>

<style lang="scss" scoped>
.agreement-dialog {
  ::v-deep .el-dialog {
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
  }
  
  ::v-deep .el-dialog__body {
    padding: 0;
  }
  
  ::v-deep .el-dialog__header {
    padding: 0;
    margin: 0;
  }
  
  ::v-deep .el-dialog__headerbtn {
    display: none;
  }
}

.agreement-header {
  padding: 16px 16px 10px;
  position: relative;
  text-align: center;
  
  .close-btn {
    position: absolute;
    top: 8px;
    right: 16px;
    cursor: pointer;
    font-size: 24px;
    color: #909399;
    
    &:hover {
      color: #606266;
    }
  }
  
  .agreement-title {
    margin: 0;
    font-size: 18px;
    font-weight: bold;
    color: #303133;
  }
  
  .agreement-desc {
    margin: 10px 0 0;
    font-size: 14px;
    color: #606266;
    line-height: 1.5;
  }
  
  .agreement-tips {
    margin: 10px 0 0;
    font-size: 13px;
    color: #E6A23C;
    line-height: 1.5;
  }
}

.agreement-content {
  height: 350px;
  max-height: 60vh;
  padding: 15px 20px;
  overflow-y: auto;
  border-top: 1px solid #ebeef5;
  border-bottom: 1px solid #ebeef5;
  background-color: #fafafa;
  
  .agreement-section {
    margin-bottom: 16px;
    
    .section-title {
      margin: 0 0 8px;
      font-size: 15px;
      font-weight: bold;
      color: #303133;
    }
    
    .section-content {
      margin: 0;
      font-size: 13px;
      color: #606266;
      line-height: 1.6;
      text-align: justify;
      word-break: break-word;
    }
  }
  
  .agreement-bottom {
    margin-top: 20px;
    padding-top: 16px;
    border-top: 1px solid #ebeef5;
    font-size: 13px;
    color: #606266;
    line-height: 1.6;
  }
}

/* 修改滚动条样式 */
.agreement-content::-webkit-scrollbar {
  width: 6px;
}

.agreement-content::-webkit-scrollbar-thumb {
  background-color: #c0c4cc;
  border-radius: 3px;
}

.agreement-content::-webkit-scrollbar-track {
  background-color: #f4f4f5;
}
</style> 