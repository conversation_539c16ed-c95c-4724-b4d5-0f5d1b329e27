<template>
  <div class="page-login">
    <!-- 修改为下拉框形式的语言切换器 -->
    <div class="language-dropdown">
      <el-dropdown trigger="click" @command="onChangeLocale">
        <div class="current-language">
          <span class="flag-emoji">{{ currentLanguage.flag }}</span>
          <span>{{ currentLanguage.label }}</span>
          <i class="el-icon-arrow-down"></i>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item v-for="language in languages" :key="language.value" :command="language.value">
              <div class="language-option">
                <span class="flag-emoji">{{ language.flag }}</span>
                <span>{{ language.label }}</span>
              </div>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
<!--    <div class="page-login&#45;&#45;layer page-login&#45;&#45;layer-area">-->
<!--      <ul class="circles">-->
<!--        <li v-for="n in 10" :key="n"></li>-->
<!--      </ul>-->
<!--    </div>-->
    <!-- 协议弹窗 -->
    <AgreementDialog :visible.sync="dialogVisible" :agreement-type="agreementType" />
<!--    <div class="page-login&#45;&#45;layer page-login&#45;&#45;layer-time" flex="main:center cross:center">{{time}}</div>-->
    <div class="page-login--layer">
      <div class="page-login--content" flex="dir:top main:justify cross:stretch box:justify">
        <div class="page-login--content-header">
          <p class="page-login--content-header-motto">{{ $t('login.slogan') }}</p>
        </div>
        <div class="page-login--content-main" flex="dir:top main:center cross:center">
          <!-- logo -->
<!--          <img class="page-login&#45;&#45;logo" src="./image/<EMAIL>" />-->
<!--          <p class="rufitai-text"><span class="green-letter">M</span>arsMind.co</p>-->
          <div class="page-login--logo">
            <img src="/image/theme/marsmind/logo/marsmind.svg" alt="MarsMind Logo">
          </div>
          <!-- form -->
          <div class="page-login--form">
            <el-card shadow="never">
              <div class="tab-wrapper">
                <div :class="['tab-item', activeTab === 'login' ? 'active' : '']" @click="switchTab('login')">{{ $t('login.loginTab') }}</div>
                <div :class="['tab-item', activeTab === 'register' ? 'active' : '']" @click="switchTab('register')">{{ $t('login.registerTab') }}</div>
              </div>
              <!-- 登录表单 -->
              <el-form
                v-if="activeTab === 'login'"
                ref="loginForm"
                label-position="top"
                :model="formLogin"
                size="default"
              >
                <el-form-item prop="username" :error="loginErrors.username">
                  <el-input type="text" v-model="formLogin.username" :placeholder="$t('login.username')">
                    <template #prepend><i class="fa fa-user-circle-o"></i></template>
                  </el-input>
                </el-form-item>
                <el-form-item prop="password" :error="loginErrors.password">
                  <el-input type="password" v-model="formLogin.password" :placeholder="$t('login.password')" show-password>
                    <template #prepend><i class="fa fa-keyboard-o"></i></template>
                  </el-input>
                </el-form-item>
                <el-button size="default" @click="submitLogin" type="primary" v-loading="loading" class="button-login">{{ $t('login.loginButton') }}</el-button>
              </el-form>

              <!-- 注册表单 -->
              <el-form
                v-if="activeTab === 'register'"
                ref="registerForm"
                label-position="top"
                :model="formRegister"
                size="default"
              >
                <el-form-item prop="username" :error="registerErrors.username">
                  <el-input type="text" v-model="formRegister.username" :placeholder="$t('login.username')">
                    <template #prepend><i class="fa fa-user-circle-o"></i></template>
                  </el-input>
                </el-form-item>
                <el-form-item prop="password" :error="registerErrors.password">
                  <el-input type="password" v-model="formRegister.password" :placeholder="$t('login.password')" show-password>
                    <template #prepend><i class="fa fa-keyboard-o"></i></template>
                  </el-input>
                </el-form-item>
                <el-form-item prop="confirmPassword" :error="registerErrors.confirmPassword">
                  <el-input type="password" v-model="formRegister.confirmPassword" :placeholder="$t('login.confirmPassword')" show-password>
                    <template #prepend><i class="fa fa-keyboard-o"></i></template>
                  </el-input>
                </el-form-item>
                <el-form-item prop="inviteCode" :error="registerErrors.inviteCode">
                  <el-input type="text" v-model="formRegister.inviteCode" :placeholder="$t('login.inviteCode')">
                    <template #prepend><i class="fa fa-ticket"></i></template>
                  </el-input>
                </el-form-item>
                <el-form-item prop="agreement" :error="registerErrors.agreement" class="agreement-item">
                  <el-checkbox v-model="formRegister.agreement">
                    <span class="agreement-text">{{ $t('login.agreementPrefix') }} <a href="javascript:void(0)" @click.prevent="showAgreement('TermOfService')">{{ $t('login.termOfService') }}</a>，<a href="javascript:void(0)" @click.prevent="showAgreement('privacyPolicy')">{{ $t('login.privacyPolicy') }}</a>{{ $t('login.agreementConnector') }} <a href="javascript:void(0)" @click.prevent="showAgreement('dataUsageAgreement')">{{ $t('login.dataUsageAgreement') }}</a></span>
                  </el-checkbox>
                </el-form-item>
                <el-button size="default" @click="submitRegister" type="primary" v-loading="registerLoading" class="button-login">{{ $t('login.registerButton') }}</el-button>
              </el-form>
            </el-card>
          </div>
        </div>
        <div class="page-login--content-footer">
          <p class="page-login--content-footer-copyright">
            Copyright
            <d2-icon name="copyright" />2025 MarsMind.co
<!--            <a href="https://blog.hb0730.com">@hb0730</a>-->
          </p>
<!--          <p class="page-login&#45;&#45;content-footer-options">-->
<!--            <a href="#">帮助</a>-->
<!--            <a href="#">隐私</a>-->
<!--            <a href="#">条款</a>-->
<!--          </p>-->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import dayjs from "dayjs";
import { mapActions } from "vuex";
import localeMixin from "@/locales/mixin.js";
import AgreementDialog from "./components/AgreementDialog.vue";
import api from "@/api";
export default {
  components: {
    AgreementDialog
  },
  mixins: [localeMixin],
  data() {
    const validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入密码'));
      } else {
        if (this.formRegister.confirmPassword !== '') {
          this.$refs.registerForm && this.$refs.registerForm.validateField('confirmPassword');
        }
        callback();
      }
    };
    const validatePass2 = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'));
      } else if (value !== this.formRegister.password) {
        callback(new Error('两次输入密码不一致'));
      } else {
        callback();
      }
    };
    return {
      activeTab: 'login',
      loading: false,
      registerLoading: false,
      timeInterval: null,
      time: dayjs().format("HH:mm:ss"),
      dialogVisible: false,
      agreementType: 'TermOfService',
      // 语言选择器数据
      languages: [
        {
          value: 'en', 
          label: 'English',
          flag: '🇺🇸'
        },
        {
          value: 'zh-chs', 
          label: '中文',
          flag: '🇨🇳'
        },
        {
          value: 'ja', 
          label: '日本語',
          flag: '🇯🇵'
        }
      ],
      // 登录表单
      formLogin: {
        username: "",
        password: "",
      },
      // 注册表单
      formRegister: {
        username: "",
        password: "",
        confirmPassword: "",
        inviteCode: "",
        agreement: false
      },
      // 表单错误状态
      loginErrors: {
        username: '',
        password: '',
      },
      registerErrors: {
        username: '',
        password: '',
        confirmPassword: '',
        inviteCode: '',
        agreement: '',
      }
    };
  },
  computed: {
    // 获取当前选中的语言
    currentLanguage() {
      const locale = this.$i18n ? this.$i18n.locale : 'en';
      return this.languages.find(lang => lang.value === locale) || this.languages[0];
    }
  },
  mounted() {
    this.timeInterval = setInterval(() => {
      this.refreshTime();
    }, 1000);
    // 绑定enter事件
    this.enterKeyup();
  },
  beforeDestroy() {
    clearInterval(this.timeInterval);
    // 销毁enter事件
    this.enterKeyupDestroyed();
  },
  methods: {
    ...mapActions("d2admin/account", ["login"]),
    // 显示协议内容
    showAgreement(type) {
      this.dialogVisible = true;
      this.agreementType = type;
    },
    // 切换标签页
    switchTab(tab) {
      this.activeTab = tab;
      // 重置错误状态
      if (tab === 'login') {
        this.loginErrors = {
          username: '',
          password: '',
        };
      } else {
        this.registerErrors = {
          username: '',
          password: '',
          confirmPassword: '',
          inviteCode: '',
          agreement: '',
        };
      }
    },
    refreshTime() {
      this.time = dayjs().format("HH:mm:ss");
    },
    enterKeyupDestroyed() {
      document.removeEventListener("keyup", this.enterKey);
    },
    enterKeyup() {
      document.addEventListener("keyup", this.enterKey);
    },
    /**
     * @description 接收选择一个用户快速登录的事件
     * @param {Object} user 用户信息
     */
    handleUserBtnClick(user) {
      this.formLogin.username = user.username;
      this.formLogin.password = user.password;
      this.submitLogin();
    },
    enterKey(event) {
      const code = event.keyCode
        ? event.keyCode
        : event.which
        ? event.which
        : event.charCode;
      if (code == 13) {
        if (this.activeTab === 'login') {
          this.submitLogin();
        } else {
          this.submitRegister();
        }
      }
    },
    /**
     * @description 提交登录表单
     */
    submitLogin() {
      // 手动验证
      this.loginErrors = {
        username: '',
        password: '',
      };

      let valid = true;

      if (!this.formLogin.username) {
        this.loginErrors.username = this.$t('login.errorUsername');
        valid = false;
      }

      if (!this.formLogin.password) {
        this.loginErrors.password = this.$t('login.errorPassword');
        valid = false;
      }

      if (!valid) {
        return;
      }

      let _self = this;
      _self.loading = true;
      _self.login({
        username: _self.formLogin.username,
        password: _self.formLogin.password,
        to: _self.$route.query.redirect || "/",
      }).then(result => {
        _self.loading = false;
      }).catch(err => {
        _self.loading = false;
      });
    },
    /**
     * @description 提交注册表单
     */
    submitRegister() {
      // 手动验证
      this.registerErrors = {
        username: '',
        password: '',
        confirmPassword: '',
        inviteCode: '',
        agreement: '',
      };

      let valid = true;

      if (!this.formRegister.username) {
        this.registerErrors.username = this.$t('login.errorUsername');
        valid = false;
      }

      if (!this.formRegister.password) {
        this.registerErrors.password = this.$t('login.errorPassword');
        valid = false;
      }

      if (!this.formRegister.confirmPassword) {
        this.registerErrors.confirmPassword = this.$t('login.errorConfirmPassword');
        valid = false;
      } else if (this.formRegister.confirmPassword !== this.formRegister.password) {
        this.registerErrors.confirmPassword = this.$t('login.errorPasswordMatch');
        valid = false;
      }

      if (!this.formRegister.inviteCode) {
        this.registerErrors.inviteCode = this.$t('login.errorInviteCode');
        valid = false;
      }

      if (!this.formRegister.agreement) {
        this.registerErrors.agreement = this.$t('login.errorAgreement');
        valid = false;
      }

      if (!valid) {
        return;
      }

      let _self = this;
      _self.registerLoading = true;

      // 调用注册接口
      const registerData = {
        username: this.formRegister.username,
        password: this.formRegister.password,
        invitationCode: this.formRegister.inviteCode
      };

      api.USER_REGISTER(registerData)
        .then(result => {
          _self.$message.success(_self.$t('login.registerSuccess'));

          // 注册成功后直接登录
          _self.login({
            username: _self.formRegister.username,
            password: _self.formRegister.password,
            to: _self.$route.query.redirect || "/"
          }).then(() => {
            _self.registerLoading = false;
          }).catch(err => {
            _self.registerLoading = false;
            _self.switchTab('login');
            _self.$message.error(_self.$t('login.autoLoginFailed'));
          });
        }).finally(() => {
          _self.registerLoading = false;
      })
    },
  },
};
</script>

<style lang="scss">
/* 语言下拉框样式 */
.language-dropdown {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 100;
  
  .current-language {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    
    i {
      margin-left: 5px;
      font-size: 12px;
      color: #606266;
    }
  }
}

.language-option {
  display: flex;
  align-items: center;
  gap: 5px;
}

.flag-emoji {
  font-size: 16px;
  line-height: 1;
}

/* 协议文本样式 */
.agreement-text {
  display: inline-block;
  line-height: 1.4;
  font-size: 12px;
  word-wrap: break-word;
  word-break: break-word;
  white-space: normal;
  max-width: 100%;
}

.green-letter {
  color: $color-primary;
}

/* 输入框样式 */
.page-login--form {
  width: 320px;

  .el-input__inner {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
  }

  .el-card {
    border-radius: 15px;
    overflow: hidden;

    .el-card__body {
      border-radius: 15px;
    }
  }
}

.page-login {
  @extend %unable-select;
  $backgroundColor: #f0f2f5;
  // ---
  background-color: $backgroundColor;
  height: 100%;
  position: relative;
  // 层
  .page-login--layer {
    @extend %full;
    overflow: auto;
  }
  .page-login--layer-area {
    overflow: hidden;
  }
  // 时间
  .page-login--layer-time {
    font-size: 24em;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.03);
    overflow: hidden;
  }
  // 登陆页面控件的容器
  .page-login--content {
    height: 100%;
    min-height: 500px;
  }
  // header
  .page-login--content-header {
    padding: 1em 0;
    .page-login--content-header-motto {
      margin: 0px;
      padding: 0px;
      color: $color-text-normal;
      text-align: center;
      font-size: 12px;
    }
  }
  .page-login--logo:hover {
    transform: scale(1.08);
  }
  // main
  .page-login--logo {
    width: 280px;
    transition: transform 0.3s ease;
    cursor: pointer;
    margin-bottom: 20px; /* 减少底部空白 */

    img {
      width: 100%;
      height: auto;
      margin-bottom: -40px; /* 裁剪图片底部空白 */
      display: block; /* 确保没有额外空白 */
    }
  }
  // 登录表单
  .page-login--form {
    width: 380px;
    // 标签页
    .tab-wrapper {
      display: flex;
      margin-bottom: 20px;
      border-radius: 30px;
      overflow: hidden;
      background-color: #f0f2f5;
      border: none;
      padding: 3px;

      .tab-item {
        flex: 1;
        text-align: center;
        padding: 8px 0;
        cursor: pointer;
        transition: all 0.3s;
        border-radius: 30px;
        color: #606266;
        margin: 0;

        &.active {
          background-color: #fff;
          color: #303133;
          font-weight: 500;
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
      }
    }
    // 卡片
    .el-card {
      margin-bottom: 15px;
    }
    // 登录按钮
    .button-login {
      width: 100%;
    }
    // 输入框左边的图表区域缩窄
    .el-input-group__prepend {
      padding: 0px 14px;
    }
    .login-code {
      height: 40px - 2px;
      display: block;
      margin: 0px -20px;
      border-top-right-radius: 2px;
      border-bottom-right-radius: 2px;
    }
    // 登陆选项
    .page-login--options {
      margin: 0px;
      padding: 0px;
      font-size: 14px;
      color: $color-primary;
      margin-bottom: 15px;
      font-weight: bold;
    }
    .page-login--quick {
      width: 100%;
    }
  }
  // 快速选择用户面板
  .page-login--quick-user {
    @extend %flex-center-col;
    padding: 10px 0px;
    border-radius: 4px;
    &:hover {
      background-color: $color-bg;
      i {
        color: $color-text-normal;
      }
      span {
        color: $color-text-normal;
      }
    }
    i {
      font-size: 36px;
      color: $color-text-sub;
    }
    span {
      font-size: 12px;
      margin-top: 10px;
      color: $color-text-sub;
    }
  }
  // footer
  .page-login--content-footer {
    padding: 1em 0;
    .page-login--content-footer-copyright {
      padding: 0px;
      margin: 0px;
      margin-bottom: 10px;
      font-size: 12px;
      line-height: 12px;
      text-align: center;
      color: $color-text-normal;
      a {
        color: $color-text-normal;
      }
    }
  }
  // 背景
  .circles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    margin: 0px;
    padding: 0px;
    li {
      position: absolute;
      display: block;
      list-style: none;
      width: 20px;
      height: 20px;
      background: #fff;
      animation: animate 25s linear infinite;
      bottom: -200px;
      @keyframes animate {
        0% {
          transform: translateY(0) rotate(0deg);
          opacity: 1;
          border-radius: 0;
        }
        100% {
          transform: translateY(-1000px) rotate(720deg);
          opacity: 0;
          border-radius: 50%;
        }
      }
      &:nth-child(1) {
        left: 15%;
        width: 80px;
        height: 80px;
        animation-delay: 0s;
      }
      &:nth-child(2) {
        left: 5%;
        width: 20px;
        height: 20px;
        animation-delay: 2s;
        animation-duration: 12s;
      }
      &:nth-child(3) {
        left: 70%;
        width: 20px;
        height: 20px;
        animation-delay: 4s;
      }
      &:nth-child(4) {
        left: 40%;
        width: 60px;
        height: 60px;
        animation-delay: 0s;
        animation-duration: 18s;
      }
      &:nth-child(5) {
        left: 65%;
        width: 20px;
        height: 20px;
        animation-delay: 0s;
      }
      &:nth-child(6) {
        left: 75%;
        width: 150px;
        height: 150px;
        animation-delay: 3s;
      }
      &:nth-child(7) {
        left: 35%;
        width: 200px;
        height: 200px;
        animation-delay: 7s;
      }
      &:nth-child(8) {
        left: 50%;
        width: 25px;
        height: 25px;
        animation-delay: 15s;
        animation-duration: 45s;
      }
      &:nth-child(9) {
        left: 20%;
        width: 15px;
        height: 15px;
        animation-delay: 2s;
        animation-duration: 35s;
      }
      &:nth-child(10) {
        left: 85%;
        width: 150px;
        height: 150px;
        animation-delay: 0s;
        animation-duration: 11s;
      }
    }
  }
}
</style>
