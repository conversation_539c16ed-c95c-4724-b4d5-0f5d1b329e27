<template>
  <d2-container class="page">
    <el-form ref="searchForm" :model="searchInfo" :inline="true" :label-position="position">
      <el-form-item>
        <el-date-picker
          v-model="searchInfo.date"
          type="daterange"
          align="right"
          unlink-panels
          value-format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="pickerOptions">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-select v-model="searchInfo.channelType" placeholder="IM渠道" clearable>
          <el-option
            v-for="item in channelTypeOptions"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input v-model="searchInfo.customerName" placeholder="客户名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="searchInfo.botName" placeholder="角色" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button class="filter-item el-button--small" @click="searchChange(1)" icon="fa fa-search">查询</el-button>
      </el-form-item>
    </el-form>
    <el-col :xs="10">
      <el-table
        :data="customerSessionList"
        style="width: 100%;"
        :fit="true"
        :header-cell-style="{'text-align':'center'}"
      >
        <el-table-column
          sortable
          resizable
          :show-overflow-tooltip="true"
          align="center"
          type="selection"
        ></el-table-column>
        <el-table-column
          prop="statDate"
          label="日期"
          sortable
          resizable
          :show-overflow-tooltip="true"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="channelType"
          label="IM渠道"
          sortable
          resizable
          :show-overflow-tooltip="true"
          align="center"
        >
          <template slot-scope="scope">
            {{ getDictEntryInfo('business_channel_type', scope.row.channelType) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="customerName"
          label="客户名称"
          sortable
          resizable
          :show-overflow-tooltip="true"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="botName"
          label="角色"
          sortable
          resizable
          :show-overflow-tooltip="true"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="observationCount"
          label="观测量"
          sortable
          resizable
          :show-overflow-tooltip="true"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="observationDod"
          label="观测DoD%"
          sortable
          resizable
          :show-overflow-tooltip="true"
          align="center"
        >
          <template slot-scope="scope">
            {{ scope.row.observationDod }}%
          </template>
        </el-table-column>
        <el-table-column
          prop="receivedCount"
          label="收到量"
          sortable
          resizable
          :show-overflow-tooltip="true"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="receivedDod"
          label="收到DoD%"
          sortable
          resizable
          :show-overflow-tooltip="true"
          align="center"
        >
          <template slot-scope="scope">
            {{ scope.row.receivedDod }}%
          </template>
        </el-table-column>
        <el-table-column
          prop="correctRate"
          label="正答率"
          sortable
          resizable
          :show-overflow-tooltip="true"
          align="center"
        >
          <template slot-scope="scope">
            {{ scope.row.correctRate }}%
          </template>
        </el-table-column>
        <el-table-column
          prop="learningCount"
          label="学习量"
          sortable
          resizable
          :show-overflow-tooltip="true"
          align="center"
        ></el-table-column>
      </el-table>
      <el-pagination
        align="left"
        @size-change="handleSizeChange"
        @current-change="searchChange"
        :current-page="searchInfo.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="searchInfo.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="Number(searchInfo.total)"
      ></el-pagination>
    </el-col>
  </d2-container>
</template>

<script>


import api from "@/api";
import util from "@/libs/util";

export default {
  data() {
    return {
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      searchInfo: {},
      position: 'left',
      customerSessionList: [],
      channelTypeOptions: [],
    }
  },
  methods: {
    initDict() {
      let _self = this;
      _self.channelTypeOptions = util.dict.getDictValue("business_channel_type");
    },
    getDictEntryInfo(type, entryValue) {
      if (!entryValue) {
        return ""
      }
      let dictEntryValue = util.dict.getDictEntryValue(type, entryValue);
      if (!dictEntryValue) {
        return entryValue;
      }
      return dictEntryValue.label;
    },
    handleSizeChange(pageSize) {
      let _self = this;
      _self.searchInfo.pageSize = pageSize;
      _self.getPage();
    },
    searchChange(pageNum) {
      let _self = this;
      _self.searchInfo.pageNum = pageNum;
      _self.getPage();
    },
    /**
     * 分页查询
     */
    getPage() {
      let _self = this;
      if (_self.searchInfo.date) {
        _self.searchInfo.startStrDate = _self.searchInfo.date[0];
        _self.searchInfo.endStrDate = _self.searchInfo.date[1];
      }
      let params = JSON.parse(JSON.stringify(_self.searchInfo));
      api.customerSessionListPage(params).then((result) => {
        _self.customerSessionList = result.records;
        _self.searchInfo.total = Number(result.total);
      });
    },
  },
  mounted() {
    let _self = this;
    _self.getPage();
    _self.initDict();
  },
}
</script>

<style scoped lang="scss">

</style>

