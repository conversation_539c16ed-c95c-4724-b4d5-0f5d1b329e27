<template>
  <d2-container-frame v-if="dashboardUrl" :src="dashboardUrl"/>
  <div v-else class="no-dashboard-message">
    {{ $t('dashboard.noDashboard') }}
  </div>
</template>

<script>
import {mapActions} from 'vuex'
import util from '@/libs/util.dict'
import {Message} from 'element-ui'

export default {
  name: "dashboard",
  data() {
    return {
      dashboardUrl: '',
      loading: false
    }
  },
  created() {
    this.getDashboardUrl()
  },
  methods: {
    ...mapActions("bootAdmin/user", [
      "currentUserInfo",
    ]),
    ...mapActions('d2admin/page', [
      'close'
    ]),
    async getDashboardUrl() {
      try {
        this.loading = true
        // 获取当前登录用户信息
        const userInfo = await this.currentUserInfo()
        if (!userInfo || !userInfo.deptId) {
          this.showErrorMessage()
          return
        }

        // 获取数据字典
        const dictList = util.getDictValue('business_dashboard_url')
        if (!dictList || dictList.length === 0) {
          this.showErrorMessage()
          return
        }

        // 查找对应部门的仪表盘URL
        const deptEntry = dictList.find(item => item.label === userInfo.deptId)
        if (deptEntry && deptEntry.value) {
          this.dashboardUrl = deptEntry.value
        } else {
          this.showErrorMessage()
        }
      } catch (error) {
        console.error('获取仪表盘URL失败:', error)
        this.showErrorMessage()
      } finally {
        this.loading = false
      }
    },
    showErrorMessage() {
      Message({
        message: this.$t('dashboard.contactAdmin'),
        type: 'warning',
        duration: 5000
      })

      // 关闭当前页面
      this.$nextTick(() => {
        const fullPath = this.$route.fullPath
        this.close({tagName: fullPath})
      })
    }
  }
}
</script>

<style scoped lang="scss">

.no-dashboard-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 16px;
  color: #909399;
}
</style>
