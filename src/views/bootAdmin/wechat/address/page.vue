<template>
  <d2-container class="page">
    <el-form ref="searchForm" :model="searchInfo" :inline="true" :label-position="position">
      <el-form-item>
        <el-select v-model="searchInfo.wcId" :placeholder="$t('wechat.account.wechatNumber')" @change="wechatNumberChange"
                   clearable>
          <el-option
            v-for="item in wechatAccountList"
            :label="item.nickName"
            :value="item.wcId">
            <span style="float: left">{{ item.nickName }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">{{ item.wechatNumber }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="searchInfo.labelList" :placeholder="$t('wechat.address.labelColumn')" clearable>
          <el-option
            v-for="item in labelList"
            :label="item.labelName"
            :value="item.labelId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="userInfo.isAdmin === 1">
        <el-input v-model="searchInfo.addressWechatNumber" :placeholder="$t('wechat.address.friend')" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="searchInfo.addressNickName" :placeholder="$t('wechat.address.nickname')" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="searchInfo.addressType" :placeholder="$t('wechat.address.type')" clearable>
          <el-option
            v-for="item in addressTypeOptions"
            :key="Number(item.value)"
            :label="translateAddressType(Number(item.value))"
            :value="Number(item.value)"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button class="filter-item el-button--small" @click="searchChange(1)" icon="fa fa-search">{{ $t('wechat.address.search') }}</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="2">
      <div class="avue-crud__menu">
        <div class="avue-crud__left">
          <el-button
            type="primary"
            @click="batchSendText"
            class="el-button filter-item el-button--success el-button--mini"
          >
            <span>{{ $t('wechat.address.batchMessage') }}</span>
          </el-button>
        </div>
        <div class="avue-crud__right">
          <el-button
            type="button"
            class="el-button el-tooltip el-button--default el-button--small is-circle"
            aria-describedby="el-tooltip-2497"
            tabindex="0"
            @click="getPage"
            :title="$t('wechat.address.refresh')"
          >
            <i class="el-icon-refresh"></i>
          </el-button>
        </div>
      </div>
      <el-col :xs="10">
        <el-table
          :data="addressList"
          style="width: 100%;"
          ref="addressListRef"
          @selection-change="handleSelectionChange"
          :fit="true"
          :header-cell-style="{'text-align':'center'}"
        >
          <el-table-column
            sortable
            resizable
            :show-overflow-tooltip="true"
            align="center"
            type="selection"
          ></el-table-column>
          <el-table-column
            prop="addressWechatAvatar"
            :label="$t('wechat.address.avatarColumn')"
            width="130px"
            sortable
            resizable
            align="center"
          >
            <template slot-scope="scope">
              <el-avatar :src="scope.row.addressWechatAvatar"></el-avatar>
            </template>
          </el-table-column>
          <el-table-column
            v-if="userInfo.isAdmin === 1"
            prop="wcId"
            :label="$t('wechat.address.wcIdColumn')"
            width="100px"
            sortable
            resizable
            :show-overflow-tooltip="true"
            align="center"
          ></el-table-column>
          <el-table-column
            v-if="userInfo.isAdmin === 1"
            prop="wechatNumber"
            :label="$t('wechat.address.accountColumn')"
            width="150px"
            sortable
            resizable
            :show-overflow-tooltip="true"
            align="center"
          ></el-table-column>
          <el-table-column
            v-if="userInfo.isAdmin === 1"
            prop="addressWcId"
            :label="$t('wechat.address.friendWcIdColumn')"
            width="150px"
            sortable
            resizable
            :show-overflow-tooltip="true"
            align="center"
          ></el-table-column>
          <el-table-column
            v-if="userInfo.isAdmin === 1"
            prop="addressWechatNumber"
            :label="$t('wechat.address.friendAccountColumn')"
            width="150px"
            sortable
            resizable
            :show-overflow-tooltip="true"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="addressNickName"
            :label="$t('wechat.address.nicknameColumn')"
            sortable
            resizable
            width="200px"
            :show-overflow-tooltip="true"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="labelList"
            :label="$t('wechat.address.labelColumn')"
            sortable
            resizable
            width="150px"
            :show-overflow-tooltip="true"
            align="center"
          >
            <template slot-scope="scope">
              {{ labelListConversion(scope.row) }}
            </template>

          </el-table-column>
          <el-table-column
            prop="addressType"
            :label="$t('wechat.address.typeColumn')"
            width="150px"
            sortable
            resizable
            :show-overflow-tooltip="true"
            align="center"
          >
            <template slot-scope="scope">
              <el-tag
                :type="scope.row.addressType === 1 ? 'success' : 'info'"
                disable-transitions
              >{{ translateAddressType(scope.row.addressType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="sex"
            :label="$t('wechat.address.genderColumn')"
            sortable
            resizable
            :show-overflow-tooltip="true"
            align="center"
          >
            <template slot-scope="scope">
              <el-tag
                :type="scope.row.sex === 0 ? 'warning' : scope.row.sex === 1 ? 'info' : 'danger'"
                disable-transitions
              >{{ translateGender(scope.row.sex) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="createTime"
            :label="$t('wechat.address.createTimeColumn')"
            sortable
            width="130px"
            resizable
            :show-overflow-tooltip="true"
            align="center"
          ></el-table-column>
        </el-table>
        <el-pagination
          align="left"
          @size-change="handleSizeChange"
          @current-change="searchChange"
          :current-page="searchInfo.pageNum"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="searchInfo.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="Number(searchInfo.total)"
        ></el-pagination>
      </el-col>
    </el-row>

    <el-dialog
      width="30%"
      :title="$t('wechat.address.dialog.title')"
      :before-close="handleDialogClose"
      :visible.sync="dialogTableVisible"
      :destroy-on-close="!dialogTableVisible"
    >
      <div style="display: flex; align-items: center;">
        <p style="margin-right: 10px;">{{ $t('wechat.address.dialog.messageType') }}：</p>
        <el-select v-model="fileType" :placeholder="$t('wechat.address.type')">
          <el-option
            v-for="item in fileTypeOptions"
            :key="item.value"
            :label="$t(item.labelKey)"
            :value="item.value">
          </el-option>
        </el-select>
      </div>

      <div style="margin-top: 20px" v-show="fileType === 1">
        <el-input
          type="textarea"
          :rows="2"
          :placeholder="$t('wechat.address.dialog.inputPlaceholder')"
          v-model="messageTextArea">
        </el-input>
        <el-button
          style="margin-top: 20px"
          type="primary"
          :loading="sendMessageLoading"
          @click="sendTxtMessage"
          class="el-button filter-item el-button--success el-button--mini"
        >
          <i class="fa fa-plus"></i>
          <span>{{ $t('wechat.address.dialog.send') }}</span>
        </el-button>
      </div>

      <div style="margin-top: 20px" v-show="fileType === 2">
        <el-input
          type="textarea"
          :rows="2"
          :placeholder="$t('wechat.address.dialog.inputPlaceholder')"
          v-model="messageTextArea">
        </el-input>
        <el-upload
          style="margin-top: 20px"
          ref="uploadRef"
          :file-list="fileList"
          :on-change="handleFileChange"
          action=""
          :auto-upload="false">
          <el-button slot="trigger"
                     :loading="sendMessageLoading"
                     class="el-button filter-item el-button--success el-button--mini" size="small"
                     type="primary">
            <i class="el-icon-circle-plus-outline"></i>
            {{ $t('wechat.address.dialog.selectFile') }}
          </el-button>
          <el-button
            :loading="sendMessageLoading"
            style="margin-left: 20px"
            type="primary"
            @click="sendFileMessage"
            class="el-button filter-item el-button--success el-button--mini"
          >
            <i class="el-icon-upload"></i>
            {{ $t('wechat.address.dialog.send') }}
          </el-button>
          <div slot="tip" class="el-upload__tip" style="color: red">{{ $t('wechat.address.dialog.fileTip') }}</div>
        </el-upload>
      </div>
    </el-dialog>
  </d2-container>
</template>
<script>
import {mapActions} from "vuex";
import util from "@/libs/util";
import api from "@/api"

export default {
  data() {
    return {
      userInfo:{},
      //检索
      searchInfo: {
        sortColumn: [],
        groupColumn: [],
        pageSize: 10,
        pageNum: 1,
        total: 0,
        wId: '',
        wechatNumber: '',
        addressWechatNumber: '',
        addressNickName: '',
        addressType: '',
        labelList: '',
      },
      position: "left",
      addressTypeOptions: [],
      // 账号列表
      addressList: [],
      multipleSelection: [],
      wechatAccountList: [],
      labelList: [],
      wechatLabelList: {},
      dialogTableVisible: false,
      messageTextArea: '',
      fileType: 1,
      fileTypeOptions: [
        {value: 1, labelKey: 'wechat.address.dialog.textMessage'},
        {value: 2, labelKey: 'wechat.address.dialog.fileMessage'}
      ],
      fileList: [],
      maxFileCount: 3,
      maxFileSize: 3 * 1024 * 1024, // 3MB
      sendMessageLoading: false,
    };
  },
  mounted() {
    let _self = this;
    _self.getPage();
    _self.initDict();
    _self.getAccountList();
    _self.getLabelList();
    _self.getCurrentUserInfo();
  },
  methods: {
    ...mapActions("bootAdmin/user", [
      "currentUserInfo",
    ]),
    /**
     * 数据字典
     */
    initDict() {
      let _self = this;
      _self.addressTypeOptions = util.dict.getDictValue("business_wechat_address_type");
    },
    getDictEntryInfo(type, entryValue) {
      return util.dict.getDictEntryValue(type, entryValue);
    },
    labelListConversion(row) {
      if (!row.labelList) {
        return ''
      }
      let label = '';
      const list = row.labelList.split(',');
      for (let i = 0; i < list.length; i++) {
        const wechatLabelList = this.wechatLabelList[row.wechatNumber];
        if (wechatLabelList) {
          for (let j = 0; j < wechatLabelList.length; j++) {
            if (Number(list[i]) === wechatLabelList[j].labelId) {
              label = label + wechatLabelList[j].labelName + " "
            }
          }
        }
      }
      return label
    },
    /**
     * 关闭弹出
     */
    handleDialogClose() {
      let _self = this;
      _self.dialogTableVisible = false;
      _self.messageTextArea = '';
      _self.fileList = [];
      _self.getPage();
    },
    handleSizeChange(pageSize) {
      let _self = this;
      _self.searchInfo.pageSize = pageSize;
      _self.getPage();
    },
    searchChange(pageNum) {
      let _self = this;
      _self.searchInfo.pageNum = pageNum;
      _self.getPage();
    },
    /**
     * 分页查询
     */
    getPage() {
      let _self = this;
      let params = JSON.parse(JSON.stringify(_self.searchInfo));
      api.wechatAddressListPage(params).then((result) => {
        _self.addressList = result.records;
        _self.searchInfo.total = Number(result.total);
      });
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    batchSendText() {
      if (this.multipleSelection.length === 0) {
        this.$message.error(this.$t('wechat.address.message.selectFriend'));
        return
      }
      this.dialogTableVisible = true
    },
    async getAccountList() {
      await api.wechatAccountList().then((result) => {
        if (result) {
          this.wechatAccountList = result
        }
      });
    },
    async getLabelList() {
      await api.wechatLabelList().then((result) => {
        if (result) {
          this.wechatLabelList = result
        }
      });
    },
    wechatNumberChange(val) {
      this.labelList = this.wechatLabelList[val];
    },
    async sendTxtMessage() {
      this.sendMessageLoading = true
      const idList = this.multipleSelection.map(item => item.id);
      let params = {
        idList: idList,
        content: this.messageTextArea
      }
      api.wechatMessageSendText(params).then((result) => {
        if (result) {
          this.$message.info(this.$t('wechat.address.message.sendSuccess'));
          this.handleDialogClose()
        }
        this.sendMessageLoading = false
      }).catch(err => {
        this.sendMessageLoading = false
      })
    },
    async sendFileMessage() {
      this.sendMessageLoading = true
      const idList = this.multipleSelection.map(item => item.id);
      const formData = new FormData();
      this.fileList.forEach((file) => {
        formData.append('files', file.raw);
      });
      idList.forEach((id) => {
        formData.append('idList', id)
      })
      formData.append('content', this.messageTextArea)

      await api.wechatMessageSendFile(formData, {'Content-Type': 'multipart/form-data'}).then((result) => {
        if (result) {
          this.$message.info(this.$t('wechat.address.message.sendSuccess'));
          this.handleDialogClose()
        }
        this.sendMessageLoading = false
      }).catch(err => {
        this.sendMessageLoading = false
      })

    },
    // 处理文件选择变化
    handleFileChange(file, fileList) {
      // 检查文件数量
      if (fileList.length > this.maxFileCount) {
        this.$message.error(this.$t('wechat.address.message.fileCountLimit'));
        // 移除当前新添加的文件
        const index = fileList.indexOf(file);
        if (index > -1) {
          fileList.splice(index, 1);
        }
        this.fileList = [...fileList]; // 更新 fileList
        return;
      }
      // 检查文件大小
      if (file.size > this.maxFileSize) {
        this.$message.error(this.$t('wechat.address.message.fileSizeLimit'));
        // 移除当前新添加的文件
        const index = fileList.indexOf(file);
        if (index > -1) {
          fileList.splice(index, 1);
        }
        this.fileList = [...fileList]; // 更新 fileList
        return;
      }
      this.fileList = fileList;
    },
    uploadFile() {
      console.log(this.fileList);
    },
    /**
     * 获取当前用户信息
     */
    getCurrentUserInfo() {
      let _self = this;
      _self.currentUserInfo().then((result) => {
        _self.userInfo = result;
      });
    },
    // 翻译通讯录类型
    translateAddressType(value) {
      if (value === 1) {
        return this.$t('wechat.address.typeOptions.friend');
      } else if (value === 2) {
        return this.$t('wechat.address.typeOptions.group');
      } else {
        return value;
      }
    },
    // 翻译性别
    translateGender(value) {
      if (value === 0) {
        return this.$t('wechat.address.genderOptions.other');
      } else if (value === 1) {
        return this.$t('wechat.address.genderOptions.male');
      } else if (value === 2) {
        return this.$t('wechat.address.genderOptions.female');
      } else {
        return value;
      }
    },
  },
};
</script>
<style scoped>

</style>
