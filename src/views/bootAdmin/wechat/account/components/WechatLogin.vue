<template>
  <el-dialog
    width="40%"
    title=""
    :before-close="handleDialogClose"
    :visible.sync="dialogVisible"
    :destroy-on-close="!dialogVisible"
    custom-class="wechat-login-dialog"
  >
    <div class="login-title">{{ $t('wechat.account.loginDialog.title') }}</div>
    <div class="login-subtitle">{{ $t('wechat.account.loginDialog.subtitle') }}</div>

    <div class="custom-steps">
      <div class="step-item" :class="{ active: activeStep >= 0, completed: activeStep > 0 }">
        <div class="step-icon">
          <i class="el-icon-location-outline"></i>
        </div>
        <div class="step-label">{{ $t('wechat.account.loginDialog.steps.selectRegion') }}</div>
      </div>
      <div class="step-line" :class="{ active: activeStep > 0 }"></div>
      <div class="step-item" :class="{ active: activeStep >= 1, completed: activeStep > 1 }">
        <div class="step-icon">
          <d2-icon-svg v-if="activeStep > 1" name="qrcode_white" class="qrcode-svg"/>
          <d2-icon-svg v-else name="qrcode" class="qrcode-svg"/>
        </div>
        <div class="step-label">{{ $t('wechat.account.loginDialog.steps.scanQrcode') }}</div>
      </div>
      <div class="step-line" :class="{ active: activeStep > 1 }"></div>
      <div class="step-item" :class="{ active: activeStep >= 2 }">
        <div class="step-icon">
          <i class="el-icon-check"></i>
        </div>
        <div class="step-label">{{ $t('wechat.account.loginDialog.steps.loginSuccess') }}</div>
      </div>
    </div>

    <!-- 步骤一：选择地区 -->
    <div v-show="activeStep === 0" class="step-content card-container">
      <div class="icon-container">
        <div class="big-icon-circle">
          <i class="el-icon-location-outline"></i>
        </div>
      </div>
      <div class="step-title">{{ $t('wechat.account.loginDialog.region.title') }}</div>
      <div class="step-subtitle">{{ $t('wechat.account.loginDialog.region.subtitle') }}</div>
      <el-select style="margin: 20px 0; width: 100%; max-width: 320px;" @change="loginRegionChange" v-model="loginRegion"
                 :placeholder="$t('wechat.account.loginDialog.region.placeholder')">
        <el-option
          v-for="item in loginRegionOptions"
          :key="Number(item.value)"
          :label="item.label"
          :value="Number(item.value)"
        ></el-option>
      </el-select>
      <div>
        <el-button class="theme-btn" type="success" @click="generateQRCode">{{ $t('wechat.account.loginDialog.region.generateBtn') }}</el-button>
      </div>
    </div>

    <!-- 步骤二：扫描二维码 -->
    <div v-show="activeStep === 1 && !initializingContacts" class="step-content card-container">
      <div class="icon-container">
        <div class="big-icon-circle">
          <d2-icon-svg name="qrcode" class="big-qrcode-svg"/>
        </div>
      </div>
      <div class="step-title">{{ $t('wechat.account.loginDialog.qrcode.title') }}</div>
      <div class="step-subtitle">{{ $t('wechat.account.loginDialog.qrcode.subtitle') }}</div>
      <el-image :src="wechatLoginUrl" style="width: 180px; height: 180px; margin: 0px 0" fit="cover">
        <!-- 图片加载中时的占位内容 -->
        <template #loading>
          <div class="image-placeholder">
            <p>{{ $t('wechat.account.loginDialog.qrcode.loading') }}</p>
          </div>
        </template>
      </el-image>
      <div class="countdown-container" v-if="remainingTime > 0">
        <span>{{ $t('wechat.account.loginDialog.qrcode.countdown') }} <span class="countdown-time">{{ remainingTime }}</span> {{ $t('wechat.account.loginDialog.qrcode.seconds') }}</span>
      </div>
    </div>

    <!-- 步骤三：登录成功 -->
    <div v-show="activeStep === 2" class="step-content card-container">
      <div class="icon-container">
        <div class="big-icon-circle">
          <i class="el-icon-success"></i>
        </div>
      </div>
      <div class="step-title">{{ $t('wechat.account.loginDialog.success.title') }}</div>
      <div class="step-subtitle">{{ $t('wechat.account.loginDialog.success.subtitle', { region: getSelectedRegionName() }) }}</div>
      <div class="login-status">
        {{ $t('wechat.account.loginDialog.success.status', { region: getSelectedRegionName() }) }}
      </div>
      <div style="margin-top: 20px">
        <el-button class="theme-btn" type="success" @click="handleDialogClose">{{ $t('wechat.account.loginDialog.success.startBtn') }}</el-button>
      </div>
    </div>

    <!-- 通讯录初始化进度条 -->
    <div v-if="initializingContacts" class="initializing-container card-container">
      <div class="icon-container">
        <div class="big-icon-circle">
          <i class="el-icon-mobile-phone"></i>
        </div>
      </div>
      <div class="step-title">{{ $t('wechat.account.loginDialog.initializing.title') }}</div>
      <div class="step-subtitle">{{ $t('wechat.account.loginDialog.initializing.subtitle') }}</div>
      <el-progress :percentage="contactsInitPercentage" :stroke-width="6" :show-text="false" style="margin: 15px 0 10px;"></el-progress>
      <div class="progress-text">{{ $t('wechat.account.loginDialog.initializing.progress') }} {{ Math.floor(contactsInitPercentage) }}%</div>
      <div class="loading-dots">
        <span></span>
        <span></span>
        <span></span>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import api from "@/api";
import util from "@/libs/util";

export default {
  name: "WechatLogin",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    selectedAccounts: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      activeStep: 0,
      dialogVisible: false,
      wechatLoginUrl: '',
      remainingTime: 0,
      timer: null,
      loginRegionOptions: [],
      loginRegion: null,
      initializingContacts: false,
      contactsInitPercentage: 0,
      qrCodeShow: false,
      loginLoading: false,
    };
  },
  watch: {
    visible(val) {
      this.loginRegion = null;
      this.dialogVisible = val;
    },
    dialogVisible(val) {
      this.$emit('update:visible', val);
    }
  },
  created() {
    this.initDict();
  },
  methods: {
    initDict() {
      // 获取地区字典并确保转换为数字值
      const dictOptions = util.dict.getDictValue("business_wechat_login_region") || [];
      this.loginRegionOptions = dictOptions.map(item => ({
        ...item,
        value: Number(item.value)
      }));

      console.log('加载地区选项:', this.loginRegionOptions);

      // 尝试从localStorage恢复loginRegion
      if (localStorage.getItem('wechat_login_region')) {
        this.loginRegion = Number(localStorage.getItem('wechat_login_region'));
      }
    },
    handleDialogClose() {
      if (this.timer) {
        clearInterval(this.timer);
      }
      this.activeStep = 0;
      this.qrCodeShow = false;
      this.initializingContacts = false;
      this.contactsInitPercentage = 0;
      this.dialogVisible = false;

      // 保留loginRegion，不要清除
      // 只有在组件销毁时才清除localStorage

      this.$emit('login-completed');
    },
    loginRegionChange(val) {
      this.qrCodeShow = true;
      this.loginRegion = val;
    },
    async generateQRCode() {
      if (!this.loginRegion) {
        this.$message.warning(this.$t('wechat.account.loginDialog.errors.selectRegion'));
        return;
      }
      this.activeStep = 1;
      await this.login(this.loginRegion);
    },
    async login(proxy) {
      let params = {
        proxy: proxy,
        wcId: this.selectedAccounts.length !== 0 ? this.selectedAccounts[0].wcId : null
      }
      let wId;
      try {
        const result = await api.wechatAccountLogin(params);
        this.wechatLoginUrl = result.qrCodeUrl;
        wId = result.wId;
        this.startCountdown();
        localStorage.setItem('wechat_login_region', this.loginRegion);
        this.getLogin(wId);
      } catch (err) {
        this.$message.error(this.$t('wechat.account.loginDialog.errors.qrcodeFailed'));
        this.activeStep = 0;
      }
    },
    async getLogin(wId) {
      if (!wId) {
        return false;
      }
      let params = {
        wid: wId,
        proxy: this.loginRegion
      };
      try {
        const result = await api.wechatAccountGetLoginInfo(params);
        if (result) {
          this.initializeContacts({...params, proxy: this.loginRegion});
        }
      } catch (err) {
        this.$message.error(this.$t('wechat.account.loginDialog.errors.loginFailed'));
      }
    },
    async initializeContacts(params) {
      this.initializingContacts = true;
      this.loginLoading = true;
      this.startContactsInitProgress();

      try {
        await api.wechatAddressInit(params);
        if (localStorage.getItem('wechat_login_region')) {
          this.loginRegion = Number(localStorage.getItem('wechat_login_region'));
        }
        setTimeout(() => {
          this.initializingContacts = false;
          this.loginLoading = false;
          this.activeStep = 2;
        }, 500);
      } catch (err) {
        this.initializingContacts = false;
        this.loginLoading = false;
        this.$message.error(this.$t('wechat.account.loginDialog.errors.initFailed'));
        this.activeStep = 0;
      }
    },
    startContactsInitProgress() {
      const totalTime = 8000;
      const interval = 100;
      const step = 100 / (totalTime / interval);

      this.contactsInitPercentage = 0;
      const timer = setInterval(() => {
        this.contactsInitPercentage += step;

        if (this.contactsInitPercentage >= 100 || !this.initializingContacts) {
          clearInterval(timer);
          this.contactsInitPercentage = 100;
        }
      }, interval);
    },
    startCountdown() {
      this.remainingTime = 180;
      if (this.timer) {
        clearInterval(this.timer);
      }
      this.timer = setInterval(() => {
        if (this.remainingTime > 0) {
          this.remainingTime--;
        } else {
          clearInterval(this.timer);
          this.$message.warning(this.$t('wechat.account.loginDialog.errors.qrcodeExpired'));
          this.activeStep = 0;
        }
      }, 1000);
    },
    getSelectedRegionName() {
      // 先检查当前组件中的loginRegion
      let regionId = this.loginRegion;

      // 如果没有，尝试从localStorage恢复
      if (!regionId && localStorage.getItem('wechat_login_region')) {
        regionId = Number(localStorage.getItem('wechat_login_region'));
      }

      // 确保loginRegionOptions已加载
      if (this.loginRegionOptions.length === 0) {
        this.initDict();
      }

      // 查找对应的地区名称
      const region = this.loginRegionOptions.find(item => Number(item.value) === Number(regionId));
      console.log('当前地区ID:', regionId, '地区选项:', this.loginRegionOptions, '匹配结果:', region);

      return region ? region.label : this.$t('wechat.account.loginDialog.unknownRegion');
    },
    // 在组件销毁时清理localStorage
    beforeDestroy() {
      localStorage.removeItem('wechat_login_region');
    }
  }
};
</script>

<style scoped>
.wechat-login-dialog {
  border-radius: 8px;
  background-color: #f5f7fa;
}

.image-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
  border: 1px solid #e4e7ed;
  color: #909399;
}

.countdown-time {
  font-size: 18px;
  font-weight: bold;
  color: #0d47a1;
}

.countdown-container {
  text-align: center;
  margin-bottom: 0px;
}

.login-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 6px;
  text-align: center;
  color: #333;
}

.login-subtitle {
  font-size: 12px;
  color: #606266;
  margin-bottom: 15px;
  text-align: center;
}

.step-content {
  text-align: center;
  margin: 20px 0;
  padding: 0 20px;
}

.card-container {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px 20px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
  width: 90%;
  max-width: 600px;
  margin: 15px auto;
}

.icon-container {
  margin-bottom: 15px;
  display: flex;
  justify-content: center;
}

.big-icon-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #f0f9eb;
  display: flex;
  align-items: center;
  justify-content: center;
}

.big-icon-circle i {
  font-size: 30px;
  color: #0d47a1;
}

.location-icon, .qrcode-icon, .success-icon {
  font-size: 48px;
  color: #0d47a1;
}

.step-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 6px;
  color: #333;
}

.step-subtitle {
  font-size: 12px;
  color: #909399;
  margin-bottom: 10px;
}

.login-status {
  font-size: 14px;
  color: #606266;
  margin: 15px 0;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.initializing-container {
  text-align: center;
  background-color: #fff;
  border-radius: 8px;
  padding: 30px 20px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
  width: 90%;
  max-width: 600px;
  margin: 30px auto;
}

.progress-text {
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
}

.loading-dots {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 6px;
}

.loading-dots span {
  display: inline-block;
  width: 6px;
  height: 6px;
  margin: 0 3px;
  background-color: #0d47a1;
  border-radius: 50%;
  animation: dot-flashing 1.4s infinite linear alternate;
}

.loading-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.loading-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes dot-flashing {
  0% {
    opacity: 0.2;
  }
  100% {
    opacity: 1;
  }
}

/* 自定义步骤条 */
.custom-steps {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 15px 0;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
}

.step-icon {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #c0c4cc;
  margin-bottom: 4px;
  border: 1px solid #dcdfe6;
  transition: all 0.3s;
}

.step-icon i {
  font-size: 14px;
}

.step-label {
  font-size: 11px;
  color: #c0c4cc;
}

.step-line {
  height: 1px;
  width: 100px;
  background-color: #dcdfe6;
  margin: 0 5px;
  position: relative;
  top: -14px;
  z-index: 1;
}

/* 激活状态 */
.step-item.active .step-icon {
  background-color: #ffffff;
  color: #0d47a1;
  border: 2px solid #0d47a1;
  box-shadow: 0 0 0 1px rgba(245, 108, 108, 0.2);
}

.step-item.active .step-label {
  color: #0d47a1;
  font-weight: bold;
}

.step-line.active {
  background-color: #0d47a1;
}

/* 已完成状态 */
.step-item.completed .step-icon {
  background-color: #0d47a1;
  color: white;
  border-color: #0d47a1;
}

/* 确保SVG图标在完成状态时可见 */
.step-item.completed .step-icon .qrcode-svg {
  color: white;
}

/* 主题按钮 */
.theme-btn {
  background-color: #0d47a1;
  border-color: #0d47a1;
  color: white;
  padding: 12px 30px;
  border-radius: 4px;
}

.theme-btn:hover, .theme-btn:focus {
  background-color: rgba(13, 71, 161, 0.8);
  border-color: rgba(13, 71, 161, 0.8);
}

.theme-btn:active {
  background-color: rgba(13, 71, 161, 0.9);
  border-color: rgba(13, 71, 161, 0.9);
}

.qrcode-svg {
  font-size: 14px;
  display: inline-block;
  width: 14px;
  height: 14px;
  color: #0d47a1;
}

.step-item.active .step-icon .qrcode-svg {
  color: #0d47a1;
}

.step-item.completed .step-icon .qrcode-svg {
  color: white;
}

.big-qrcode-svg {
  font-size: 32px;
  display: inline-block;
  width: 32px;
  height: 32px;
  color: #0d47a1;
}
</style>
