<template>
  <d2-container class="page">
    <el-form ref="searchForm" :model="searchInfo" :inline="true" :label-position="position">
      <template v-if="userInfo.isAdmin === 1">
        <el-form-item>
          <el-input v-model="searchInfo.wid" placeholder="wid" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model="searchInfo.wcId" placeholder="wcId" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model="searchInfo.wechatNumber" :placeholder="$t('wechat.account.wechatNumber')" clearable></el-input>
        </el-form-item>
      </template>
      <el-form-item>
        <el-select v-model="searchInfo.loginStatus" :placeholder="$t('wechat.account.loginStatus')" clearable>
          <el-option
            v-for="item in wechatLoginOptions"
            :key="Number(item.value)"
            :label="translateDictLabel(Number(item.value))"
            :value="Number(item.value)"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button class="filter-item el-button--small" @click="searchChange(1)" icon="fa fa-search">{{ $t('wechat.account.search') }}</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="2">
      <div class="avue-crud__menu">
        <div class="avue-crud__left">
          <el-button
            v-loading="loginLoading"
            :element-loading-text="$t('wechat.account.initializing')"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.8)"
            type="primary"
            @click="wechatLogin"
            class="el-button filter-item el-button--success el-button--mini"
          >
            <span>{{ $t('wechat.account.login') }}</span>
          </el-button>

          <el-button
            type="primary"
            @click="syncLoginStatus"
            class="el-button filter-item el-button--success el-button--mini"
          >
            <span>{{ $t('wechat.account.syncStatus') }}</span>
          </el-button>
        </div>
        <div class="avue-crud__right">
          <button
            type="button"
            class="el-button el-tooltip el-button--default el-button--small is-circle"
            aria-describedby="el-tooltip-2497"
            tabindex="0"
            @click="getPage"
            :title="$t('wechat.account.refresh')"
          >
            <i class="el-icon-refresh"></i>
          </button>
        </div>
      </div>
      <el-col :xs="10">
        <el-table
          :data="accountList"
          style="width: 100%;"
          ref="accountListRef"
          @selection-change="handleRowClick"
          :fit="true"
          :header-cell-style="{'text-align':'center'}"
        >
          <el-table-column
            sortable
            resizable
            :show-overflow-tooltip="true"
            align="center"
            type="selection"
          ></el-table-column>
          <el-table-column
            prop="wechatAvatar"
            :label="$t('wechat.account.avatar')"
            sortable
            resizable
            align="center"
          >
            <template slot-scope="scope">
              <el-avatar :src="scope.row.wechatAvatar"></el-avatar>
            </template>
          </el-table-column>
          <el-table-column
            v-if="userInfo.isAdmin === 1"
            prop="wid"
            label="wid"
            sortable
            resizable
            :show-overflow-tooltip="true"
            align="center"
          ></el-table-column>
          <el-table-column
            v-if="userInfo.isAdmin === 1"
            prop="wcId"
            label="wcId"
            sortable
            resizable
            :show-overflow-tooltip="true"
            align="center"
          ></el-table-column>
          <el-table-column
            v-if="userInfo.isAdmin === 1"
            prop="wechatNumber"
            :label="$t('wechat.account.wechatNumber')"
            sortable
            resizable
            :show-overflow-tooltip="true"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="nickName"
            :label="$t('wechat.account.nickname')"
            sortable
            resizable
            :show-overflow-tooltip="true"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="personalitySignature"
            :label="$t('wechat.account.signature')"
            sortable
            resizable
            :show-overflow-tooltip="true"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="loginStatus"
            :label="$t('wechat.account.loginStatus')"
            sortable
            resizable
            :show-overflow-tooltip="true"
            align="center"
          >
            <template slot-scope="scope">
              <el-tag
                :type="scope.row.loginStatus === 1 ? 'success' : 'danger'"
                disable-transitions
              >{{ translateDictLabel(scope.row.loginStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="createTime"
            :label="$t('wechat.account.createTime')"
            sortable
            resizable
            :show-overflow-tooltip="true"
            align="center"
          ></el-table-column>
        </el-table>
        <el-pagination
          align="left"
          @size-change="handleSizeChange"
          @current-change="searchChange"
          :current-page="searchInfo.pageNum"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="searchInfo.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="Number(searchInfo.total)"
        ></el-pagination>
      </el-col>
    </el-row>
    <wechat-login
      :visible.sync="dialogTableVisible"
      :selected-accounts="multipleSelection"
      @login-completed="getPage"
    />
  </d2-container>
</template>
<script>
import util from "@/libs/util";
import api from "@/api";
import {mapActions} from "vuex";
import WechatLogin from './components/WechatLogin';

export default {
  components: {
    WechatLogin
  },
  data() {
    return {
      userInfo: {},
      //检索
      searchInfo: {
        sortColumn: ['login_status'],
        sortType: 'desc',
        groupColumn: [],
        pageSize: 10,
        pageNum: 1,
        total: 0,
        wcId: '',
        wId: '',
        wechatNumber: '',
        loginStatus: '',
      },
      position: "left",
      wechatLoginOptions: [],
      // 账号列表
      accountList: [],
      dialogTableVisible: false,
      multipleSelection: [],
      loginLoading: false,
      syncLoginStatusLoading: false,
    };
  },
  mounted() {
    let _self = this;
    _self.getPage();
    _self.initDict();
    _self.getCurrentUserInfo();
  },
  methods: {
    ...mapActions("bootAdmin/user", [
      "currentUserInfo",
    ]),
    /**
     * 数据字典
     */
    initDict() {
      let _self = this;
      _self.wechatLoginOptions = util.dict.getDictValue("business_wechat_login_status");
    },
    getDictEntryInfo(type, entryValue) {
      return util.dict.getDictEntryValue(type, entryValue);
    },
    // 翻译字典值
    translateDictLabel(value) {
      if (value === 1) {
        return this.$t('wechat.account.status.online');
      } else {
        return this.$t('wechat.account.status.offline');
      }
    },
    handleSizeChange(pageSize) {
      let _self = this;
      _self.searchInfo.pageSize = pageSize;
      _self.getPage();
    },
    searchChange(pageNum) {
      let _self = this;
      _self.searchInfo.pageNum = pageNum;
      _self.getPage();
    },
    /**
     * 分页查询
     */
    getPage() {
      let _self = this;
      let params = JSON.parse(JSON.stringify(_self.searchInfo));
      api.wechatAccountListPage(params).then((result) => {
        _self.accountList = result.records;
        _self.searchInfo.total = Number(result.total);
      });
    },
    handleRowClick(val) {
      this.multipleSelection = val;
    },
    async syncLoginStatus() {
      this.syncLoginStatusLoading = true
      await api.syncWktWechatLoginStatus().then((result) => {
        if (result) {
          this.$message.info(this.$t('wechat.account.syncCompleted'));
          this.getPage()
        }
      });
      this.syncLoginStatusLoading = false
    },
    wechatLogin() {
      if (this.multipleSelection.length > 1) {
        this.$message.error(this.$t('wechat.account.selectOne'));
        return
      }
      if (this.multipleSelection.length === 0) {
        this.$confirm(this.$t('wechat.account.loginConfirm'), this.$t('wechat.account.tip'), {
          dangerouslyUseHTMLString: true,
          confirmButtonText: this.$t('wechat.account.confirm'),
          cancelButtonText: this.$t('wechat.account.cancel'),
          type: 'warning',
          center: true
        }).then(() => {
          this.dialogTableVisible = true
        }).catch(() => {

        });
      }
      if (this.multipleSelection.length === 1) {
        this.dialogTableVisible = true
      }
    },
    /**
     * 获取当前用户信息
     */
    getCurrentUserInfo() {
      let _self = this;
      _self.currentUserInfo().then((result) => {
        _self.userInfo = result;
      });
    },
  },
};
</script>
<style scoped>
.image-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
  border: 1px solid #e4e7ed;
  color: #909399;
}

.countdown-time {
  font-size: 25px; /* 这里可以根据需要调整字体大小 */
  font-weight: bold; /* 可根据需求添加字体加粗等样式 */
  color: #3b97d7; /* 可根据需求修改字体颜色 */
}

.countdown-container {
  text-align: center; /* 文本水平居中 */
  margin-bottom: 10px; /* 可根据需要调整与图片的间距 */
}

.login-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 10px;
  text-align: center;
}

.login-subtitle {
  font-size: 12px;
  color: #606266;
  margin-bottom: 20px;
  text-align: center;
}

.step-content {
  text-align: center;
  margin: 30px 0;
  padding: 0 20px;
}

.icon-container {
  margin-bottom: 15px;
}

.location-icon, .qrcode-icon, .success-icon {
  font-size: 48px;
  color: #409EFF;
}

.step-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
}

.step-subtitle {
  font-size: 12px;
  color: #909399;
  margin-bottom: 15px;
}

.login-status {
  font-size: 14px;
  color: #606266;
  margin: 15px 0;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.initializing-container {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.el-steps {
  margin: 20px 0;
}

.el-progress {
  margin-top: 10px;
}
</style>
