<template>
  <el-dialog title="新增配置" :visible.sync="visible" @close="handleClose" close-on-press-escape
             :close-on-click-modal="false">
    <el-form :model="form" ref="addForm" :rules="rules">
      <el-form-item label="角色ID" prop="botId">
        <el-input v-model="form.botId" clearable></el-input>
      </el-form-item>
      <el-form-item label="角色名称" prop="botName">
        <el-input v-model="form.botName" clearable></el-input>
      </el-form-item>
      <el-form-item label="跟进时效" prop="followUpTimelinessExt">
        <div style="display: flex; align-items: center;">
          <div v-for="(item, index) in followUpInputs" :key="index"
               style="display: flex; align-items: center; margin-right: 10px;">
            <el-input
              v-model.number="followUpTimelinessExt[index]"
              :placeholder="`第${index + 1}次/小时`"
              style="width: 120px;"
              @input="handleInput($event, index)"
              type="number"
            ></el-input>
          </div>
          <el-button
            v-if="followUpInputs.length < 3"
            type="primary"
            icon="el-icon-plus"
            @click="addInput"
            style="margin-left: 10px"
          >添加
          </el-button>
          <el-button
            v-if="followUpInputs.length > 1"
            type="danger"
            icon="el-icon-delete"
            @click="removeLastInput"
            style="margin-left: 10px"
          ></el-button>
        </div>
      </el-form-item>
      <el-form-item label="判断提示词" prop="judgePrompt">
        <el-input v-model="form.judgePrompt" type="textarea" :rows="5" clearable></el-input>
      </el-form-item>
      <el-form-item label="发送提示词" prop="sendPrompt">
        <el-input v-model="form.sendPrompt" type="textarea" :rows="5" clearable></el-input>
      </el-form-item>
      <el-form-item label="发送端口" prop="sendPort">
        <el-input v-model="form.sendPort" clearable></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="save">保 存</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "add",
  data() {
    // 自定义验证规则
    const validateFollowUpTimeliness = (rule, value, callback) => {
      // 检查是否有值
      const hasEmptyValue = this.followUpTimelinessExt
        .slice(0, this.followUpInputs.length)  // 只检查当前显示的输入框
        .some(item => item === '' || item === null);

      if (hasEmptyValue) {
        return callback(new Error('请输入跟进时效'))
      }

      // 检查数值范围
      const hasInvalidRange = this.followUpTimelinessExt
        .filter(item => item !== '' && item !== null) // 只验证有值的输入框
        .some(item => item < 1 || item > 48)  // 改成1-48小时的范围
      if (hasInvalidRange) {
        return callback(new Error('跟进时效必须在1-48小时之间'))
      }

      callback()
    }

    return {
      visible: false,
      followUpInputs: [1], // 控制显示的输入框数量
      followUpTimelinessExt: [''], // 存储多个时效值
      form: {
        botId: '',
        botName: '',
        followUpTimeliness: '',
        judgePrompt: '',
        sendPrompt: '',
        sendPort: ''
      },
      rules: {
        botId: [{required: true, message: "请输入角色ID", trigger: "blur"}],
        botName: [{required: true, message: "请输入角色名称", trigger: "blur"}],
        followUpTimelinessExt: [{validator: validateFollowUpTimeliness, trigger: 'change'}],
        judgePrompt: [{required: true, message: "请输入判断提示词", trigger: "blur"}],
        sendPrompt: [{required: true, message: "请输入发送提示词", trigger: "blur"}],
        sendPort: [{required: true, message: "请输入发送端口", trigger: "blur"}]
      }
    }
  },
  methods: {
    handleInput(value, index) {
      // 确保输入的是非负数
      if (value < 0) {
        this.followUpTimelinessExt[index] = 0
      }
      // 触发验证
      this.$refs.addForm.validateField('followUpTimelinessExt')
    },
    addInput() {
      if (this.followUpInputs.length < 3) {
        this.followUpInputs.push(this.followUpInputs.length + 1)
        this.followUpTimelinessExt.push('')
      }
    },
    removeLastInput() {
      this.followUpInputs.pop()
      this.followUpTimelinessExt.pop()
      // 触发验证
      this.$refs.addForm.validateField('followUpTimelinessExt')
    },
    save() {
      this.$refs['addForm'].validate((valid) => {
        if (valid) {
          // 过滤掉空值，然后用-拼接
          this.form.followUpTimeliness = this.followUpTimelinessExt
            .filter(item => item !== '' && item !== null)
            .join('-')
          this.$emit('save', this.form)
        } else {
          return false
        }
      })
    },
    // 重置表单数据
    resetForm() {
      this.form = {
        botId: '',
        botName: '',
        followUpTimeliness: '',
        judgePrompt: '',
        sendPrompt: '',
        sendPort: ''
      }
      this.followUpInputs = [1]  // 重置为只显示一个输入框
      this.followUpTimelinessExt = ['']  // 重置时效值

      // 如果表单已经挂载，重置验证结果
      if (this.$refs.addForm) {
        this.$refs.addForm.resetFields()
      }
    },
    handleClose() {
      this.resetForm()
      this.$emit('close')
    }
  },
  props: {
    isUpdate: {
      type: Boolean,
      default: false
    },
    visibleProps: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: () => {
      }
    }
  },
  watch: {
    visibleProps: {
      handler: function (val) {
        this.visible = val
      },
      immediate: true
    },
    rowData: {
      handler: function (val) {
        if (val && Object.keys(val).length > 0) {
          this.form = { ...val }  // 使用展开运算符复制对象

          // 处理 followUpTimeliness
          if (val.followUpTimeliness) {
            // 将字符串分割成数组
            const timelinessArray = val.followUpTimeliness.split('-')
            // 设置输入框数量
            this.followUpInputs = Array.from({ length: timelinessArray.length }, (_, i) => i + 1)
            // 设置时效值
            this.followUpTimelinessExt = timelinessArray
          } else {
            // 如果没有值，重置为默认状态
            this.followUpInputs = [1]
            this.followUpTimelinessExt = ['']
          }
        }
      },
      immediate: true
    },
    isUpdate: {
      handler: function (val) {
        if (!val) {
          this.resetForm();
        }
      },
      immediate: true
    }
  }
}

</script>

<style scoped lang="scss">
</style>
