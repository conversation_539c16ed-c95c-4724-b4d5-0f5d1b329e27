<template>
  <d2-container class="page">
    <el-form ref="searchForm" :model="searchInfo" :inline="true" :label-position="position">
      <el-form-item>
        <el-input v-model="searchInfo.botName" placeholder="角色" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="searchInfo.isEnabled" placeholder="状态" clearable>
          <el-option
            v-for="item in isEnabledOptions"
            :key="Number(item.value)"
            :label="item.label"
            :value="Number(item.value)"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button class="filter-item el-button--small" @click="searchChange()" icon="fa fa-search">查询</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="2">
      <div class="avue-crud__menu">
        <div class="avue-crud__left">
          <el-button
            type="primary"
            @click="visible = true"
            class="el-button filter-item el-button--success el-button--mini"
          >
            <span>新增</span>
          </el-button>
        </div>
      </div>
      <el-col :xs="10">
        <el-table
          :data="tableList"
          style="width: 100%;"
          :fit="true"
          :header-cell-style="{'text-align':'center'}"
          class="custom-tooltip-table"
        >
          <el-table-column
            sortable
            resizable
            :show-overflow-tooltip="true"
            align="center"
            type="selection"
          ></el-table-column>
          <el-table-column
            prop="botId"
            label="角色ID"
            sortable
            resizable
            :show-overflow-tooltip="true"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="botName"
            label="角色"
            sortable
            resizable
            :show-overflow-tooltip="true"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="followUpTimeliness"
            label="跟进时效"
            sortable
            resizable
            :show-overflow-tooltip="true"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="judgePrompt"
            label="判断Prompt"
            sortable
            resizable
            :show-overflow-tooltip="true"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="sendPrompt"
            label="发送Prompt"
            sortable
            resizable
            :show-overflow-tooltip="true"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="sendPort"
            label="发送端口"
            sortable
            resizable
            :show-overflow-tooltip="true"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="isEnabled"
            label="状态"
            sortable
            resizable
            :show-overflow-tooltip="true"
            align="center"
          >
            <template slot-scope="scope">
              <el-tag
                :type="scope.row.isEnabled === 1 ? 'success' : 'danger'"
                disable-transitions
              >{{ getDictEntryInfo('sys_common_status', scope.row.isEnabled).label }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="createTime"
            label="创建时间"
            sortable
            resizable
            :show-overflow-tooltip="true"
            align="center"
          ></el-table-column>
          <el-table-column label="操作" width="210px">
            <template slot-scope="scope">
              <el-button
                size="mini"
                :type="scope.row.isEnabled === 1 ? 'danger' : 'success'"
                @click="handleStatusChange(scope.$index, scope.row)">
                {{ scope.row.isEnabled === 1 ? '禁用' : '启用' }}
              </el-button>
              <el-button
                size="mini"
                @click="handleEdit(scope.$index, scope.row)">编辑
              </el-button>
              <el-button
                size="mini"
                type="danger"
                @click="handleDelete(scope.$index, scope.row)">删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          align="left"
          @size-change="handleSizeChange"
          @current-change="searchChange"
          :current-page="searchInfo.pageNum"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="searchInfo.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="Number(searchInfo.total)"
        ></el-pagination>
      </el-col>
    </el-row>
    <Add :visible-props="visible" :is-update="!!rowData.id" @close="visible = false" @save="save"
         :row-data="rowData"></Add>
  </d2-container>
</template>

<script>
import util from "@/libs/util";
import api from "@/api";
import Add from '@/views/bootAdmin/followup/config/component/add.vue'
import {cloneDeep} from 'lodash'


export default {
  components: {
    Add
  },
  data() {
    return {
      visible: false,
      searchInfo: {
        pageSize: 10,
        pageNum: 1,
        total: 0,
        sortColumn: ['create_time'],
        sortType: 'desc'
      },
      position: "left",
      tableList: [],
      isEnabledOptions: [],
      rowData: {},
    };
  },
  methods: {
    /**
     * 数据字典
     */
    initDict() {
      let _self = this;
      _self.isEnabledOptions = util.dict.getDictValue("sys_common_status");
    },
    getDictEntryInfo(type, entryValue) {
      return util.dict.getDictEntryValue(type, entryValue);
    },
    handleSizeChange(pageSize) {
      let _self = this;
      _self.searchInfo.pageSize = pageSize;
      _self.getPage();
    },
    searchChange(pageNum) {
      let _self = this;
      _self.searchInfo.pageNum = pageNum;
      _self.getPage();
    },
    /**
     * 分页查询
     */
    getPage() {
      let _self = this;
      let params = JSON.parse(JSON.stringify(_self.searchInfo));
      api.robotFollowupConfigListPage(params).then((result) => {
        _self.tableList = result.records;
        _self.searchInfo.total = Number(result.total);
      });
    },
    handleStatusChange(index, row) {
      const newStatus = row.isEnabled === 1 ? 0 : 1;
      const actionText = newStatus === 1 ? '启用' : '禁用';

      this.$confirm(`确认要${actionText}该配置吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        row.isEnabled = newStatus;
        api.robotFollowupConfigUpdateIsEnabled(row).then((result) => {
          this.$message.success(`${actionText}成功`);
          this.searchChange(this.searchInfo.pageNum);
        });
      })
    },
    handleDelete(index, row) {
      this.$confirm('确认要删除该配置吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        api.robotFollowupConfigDeleteById(row.id).then((result) => {
          this.$message.success('删除成功');
          this.searchChange(this.searchInfo.pageNum);
        });
      }).catch(() => {});
    },
    handleEdit(index, row) {
      this.rowData = cloneDeep(row)  // 使用 lodash 的深拷贝
      this.visible = true
    },
    save(val) {
      if (!val.id) {
        val.isEnabled = 0
      }

      // 根据是否存在id判断是新增还是修改
      const request = val.id
        ? api.robotFollowupConfigUpdateById(val.id, val)  // 修改
        : api.robotFollowupConfigSave(val)                // 新增

      request.then((result) => {
        this.visible = false
        this.$message.success(`${val.id ? '修改' : '保存'}成功`);
        this.searchChange(1)
        this.rowData = {}  // 清空编辑数据
      })
    },
  },
  mounted() {
    let _self = this;
    _self.getPage();
    _self.initDict();
    // _self.getCurrentUserInfo();
  },
}
</script>

<style scoped>

</style>
