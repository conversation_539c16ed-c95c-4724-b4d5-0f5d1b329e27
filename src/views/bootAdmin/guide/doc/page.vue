<template>
  <div></div>
</template>

<script>
import { mapActions } from 'vuex'
import util from '@/libs/util'

export default {
  name: "doc",
  computed: {
    docUrl() {
      const baseUrl = 'https://docs.marsmind.co'
      const locale = this.$i18n.locale

      switch (locale) {
        case 'zh-chs':
        case 'zh-CHT':
          return `${baseUrl}/cn/introduction`
        case 'ja':
          return `${baseUrl}/jp/introduction`
        case 'en':
        default:
          return `${baseUrl}/introduction`
      }
    }
  },
  methods: {
    ...mapActions('d2admin/page', [
      'close'
    ])
  },
  mounted() {
    // 打开新页面
    util.open(this.docUrl)

    // 延迟关闭当前页面，确保跳转逻辑正确执行
    setTimeout(() => {
      const fullPath = this.$route.fullPath
      this.close({ tagName: fullPath }).then(() => {
        // 如果关闭后没有自动跳转，手动跳转到首页
        if (this.$route.fullPath === fullPath) {
          this.$router.push('/index')
        }
      })
    }, 100)
  }
}
</script>

<style scoped lang="scss">

</style>
