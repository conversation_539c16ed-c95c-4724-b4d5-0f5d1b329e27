<template>
  <d2-container class="page">
    <el-form ref="searchForm" :model="searchInfo" :inline="true" :label-position="position">
      <el-form-item>
        <el-input v-model="searchInfo.applicationName" placeholder="应用名" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select style="width: 150px;" v-model="searchInfo.customer" placeholder="平台客户" clearable>
          <el-option
            v-for="(item, index) in dict.customerOptions || []"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select style="width: 150px;" v-model="searchInfo.channelType" placeholder="渠道"
                   clearable>
          <el-option
            v-for="item in dict.channelTypeOptions"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select style="width: 150px;" v-model="searchInfo.bindAccountId" placeholder="绑定账号"
                   clearable>
          <el-option
            v-for="item in dict.wechatAccountList"
            :label="item.nickName"
            :value="item.wcId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select style="width: 150px;" v-model="searchInfo.host" placeholder="主机" clearable>
          <el-option
            v-for="(item, index) in dict.hostOptions || []"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select style="width: 150px;" v-model="searchInfo.onlineStatus" placeholder="状态" clearable>
          <el-option
            v-for="(item, index) in dict.onlineStatusOptions || []"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button class="filter-item el-button--small" @click="searchChange()" icon="fa fa-search">查询</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="2">
      <div class="avue-crud__menu">
        <div class="avue-crud__left">
          <el-button
            type="primary"
            @click="handleAdd()"
            class="el-button filter-item el-button--success el-button--mini"
          >
            <span>新增</span>
          </el-button>
        </div>
      </div>
      <el-col :xs="10">
        <el-table
          :data="tableList"
          style="width: 100%;"
          :fit="true"
          :header-cell-style="{'text-align':'center'}"
        >
          <el-table-column
            sortable
            resizable
            :show-overflow-tooltip="true"
            align="center"
            type="selection"
          ></el-table-column>
          <el-table-column
            prop="channelType"
            label="渠道"
            sortable
            resizable
            :show-overflow-tooltip="true"
            align="center"
          >
            <template slot-scope="scope">
              {{
                (!scope.row.channelType) ? '-' : getDictEntryInfo('business_channel_type', scope.row.channelType).label || '-'
              }}
            </template>
          </el-table-column>
          <el-table-column
            prop="applicationName"
            label="应用名称"
            sortable
            resizable
            :show-overflow-tooltip="true"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="customer"
            label="平台客户"
            sortable
            resizable
            :show-overflow-tooltip="true"
            align="center"
          >
            <template slot-scope="scope">
              {{ (!scope.row.customer) ? '-' : getDictEntryInfo('business_customer', scope.row.customer).label || '-' }}
            </template>
          </el-table-column>
          <el-table-column
            prop="bindAccountName"
            label="绑定账号"
            sortable
            resizable
            :show-overflow-tooltip="true"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="onlineStatus"
            label="状态"
            sortable
            resizable
            :show-overflow-tooltip="true"
            align="center"
          >
            <template slot-scope="scope">
              <el-tag
                :type="scope.row.onlineStatus === 1 ? 'success' : 'danger'"
                disable-transitions
              >{{ getDictEntryInfo('business_wechat_login_status', scope.row.onlineStatus).label }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="host"
            label="主机"
            sortable
            resizable
            :show-overflow-tooltip="true"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="port"
            label="端口"
            sortable
            resizable
            :show-overflow-tooltip="true"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="createTime"
            label="创建时间"
            sortable
            resizable
            :show-overflow-tooltip="true"
            align="center"
          ></el-table-column>
          <el-table-column width="150px" label="操作">
            <template slot-scope="scope" v-if="Number.parseInt(scope.row.id) > 99999">
              <el-button
                size="mini"
                type="success"
                @click="handleStart(scope.$index, scope.row)">
                {{ scope.row.onlineStatus === 1 ? '重启' : '启动' }}
              </el-button>
              <el-button v-if="scope.row.onlineStatus === 1"
                         size="mini"
                         type="danger"
                         @click="handleStop(scope.$index, scope.row)">停止
              </el-button>
              <el-button v-if="scope.row.onlineStatus === 0"
                         size="mini"
                         @click="handleEdit(scope.$index, scope.row)">编辑
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          align="left"
          @size-change="handleSizeChange"
          @current-change="searchChange"
          :current-page="searchInfo.pageNum"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="searchInfo.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="Number(searchInfo.total)"
        ></el-pagination>
      </el-col>
    </el-row>
    <Add :visible-props="visible" :is-update="!!rowData.id" @close="visible = false" @save="save"
         :row-data="rowData" :dict="dict"></Add>
  </d2-container>
</template>

<script>
import util from "@/libs/util";
import api from "@/api";
import Add from '@/views/bootAdmin/app/info/component/add.vue'
import {cloneDeep} from "lodash";

export default {
  name: "appInfo",
  components: {
    Add
  },
  data() {
    return {
      visible: false,
      position: "left",
      tableList: [],
      searchInfo: {
        pageSize: 10,
        pageNum: 1,
        total: 0,
        sortColumn: ['create_time'],
        sortType: 'desc'
      },
      rowData: {},
      dict: {
        customerOptions: [],
        hostOptions: [],
        wechatAccountList: [],
        channelTypeOptions: [],
        onlineStatusOptions: [],
      }
    }
  },
  methods: {
    /**
     * 数据字典
     */
    initDict() {
      let _self = this;
      _self.dict.customerOptions = util.dict.getDictValue("business_customer");
      _self.dict.hostOptions = util.dict.getDictValue("business_host");
      _self.dict.channelTypeOptions = util.dict.getDictValue("business_channel_type");
      _self.dict.onlineStatusOptions = util.dict.getDictValue("business_wechat_login_status");
    },
    handleStart(index, row) {
      const newStatus = row.isEnabled === 1 ? 0 : 1;
      const actionText = newStatus === 1 ? '启动' : '重启';
      this.$confirm(`确认要${actionText}该应用吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        api.appInfoStart({'id': row.id}).then((result) => {
          this.$message.success(`${actionText}中，请稍后刷新！`);
          this.searchChange(this.searchInfo.pageNum);
        });
      })
    },
    handleStop(index, row) {
      this.$confirm(`确认要停止该应用吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        api.appInfoStop({'id': row.id}).then((result) => {
          this.$message.success(`停止中，请稍后刷新！`);
          this.searchChange(this.searchInfo.pageNum);
        });
      })
    },
    handleAdd() {
      this.rowData = {}
      this.visible = true
    },
    handleEdit(index, row) {
      this.rowData = cloneDeep(row)  // 使用 lodash 的深拷贝
      this.visible = true
    },
    getDictEntryInfo(type, entryValue) {
      return util.dict.getDictEntryValue(type, entryValue);
    },
    async getAccountList() {
      await api.wechatAccountList().then((result) => {
        if (result) {
          this.dict.wechatAccountList = result
        }
      });
    },
    handleSizeChange(pageSize) {
      let _self = this;
      _self.searchInfo.pageSize = pageSize;
      _self.getPage();
    },
    searchChange(pageNum) {
      let _self = this;
      _self.searchInfo.pageNum = pageNum;
      _self.getPage();
    },
    getPage() {
      let _self = this;
      let params = JSON.parse(JSON.stringify(_self.searchInfo));
      api.appInfoListPage(params).then((result) => {
        _self.tableList = result.records;
        _self.searchInfo.total = Number(result.total);
      });
    },
    save(val) {
      if (!val.id) {
        val.isEnabled = 0
      }

      // 根据是否存在id判断是新增还是修改
      const request = val.id
        ? api.appInfoUpdateById(val.id, val)  // 修改
        : api.appInfoSave(val)                // 新增

      request.then((result) => {
        this.visible = false
        this.$message.success(`${val.id ? '修改' : '保存'}成功`);
        this.searchChange(1)
        this.rowData = {}  // 清空编辑数据
      })
    },
  },
  mounted() {
    let _self = this;
    _self.initDict();
    _self.getAccountList();
    _self.getPage();
  },


}

</script>

<style scoped lang="scss">

</style>
