<template>
  <el-dialog title="新增应用" :visible.sync="visible" @close="handleClose" close-on-press-escape
             :close-on-click-modal="false">
    <el-form :model="form" ref="addAppForm" label-position="right" label-width="80px" :rules="rules" :inline="true">
      <el-row>
        <el-col :span="12">
          <el-form-item label="应用名称" prop="applicationName">
            <el-input v-model="form.applicationName" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="平台客户" prop="customer">
            <el-select v-model="form.customer" placeholder="平台客户" clearable>
              <el-option
                v-for="(item, index) in dict.customerOptions || []"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="渠道" prop="channelType">
            <el-select v-model="form.channelType" placeholder="渠道"
                       clearable :disabled="isUpdate">
              <el-option
                v-for="item in dict.channelTypeOptions"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="绑定账号" prop="bindAccountId">
            <el-select v-model="form.bindAccountId" placeholder="绑定账号"
                       clearable>
              <el-option
                v-for="item in dict.wechatAccountList"
                :label="item.nickName"
                :value="item.wcId">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="主机" prop="host">
            <el-select v-model="form.host" placeholder="主机" clearable :disabled="isUpdate" @change="hostChange">
              <el-option
                v-for="(item, index) in dict.hostOptions || []"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="端口" prop="port">
            <el-input v-model="form.port" clearable disabled></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>


    <div slot="footer" class="dialog-footer" style="text-align: center">
      <el-button @click="handleClose">取 消</el-button>
      <el-button style="margin-left: 30px" type="primary" @click="save">保 存</el-button>
    </div>
  </el-dialog>
</template>

<script>
import api from "@/api";


export default {
  name: "add",
  data() {
    // 自定义验证规则
    const validateAppName = (rule, value, callback) => {
      if (value && !/^[A-Za-z]+$/.test(value)) {
        callback(new Error('应用名称只能输入字母'));
      } else {
        callback();
      }
    };

    return {
      visible: false,
      form: {
        applicationName: '',
        customer: '',
        bindAccountId: '',
        bindAccountName: '',
        channelType: '',
        host: '',
        port: ''
      },
      rules: {
        applicationName: [
          {required: true, message: "请输入应用名称", trigger: "blur"},
          {validator: validateAppName, trigger: "blur"}
        ],
        customer: [{required: true, message: "请选择平台客户", trigger: "blur"}],
        channelType: [{required: true, message: "请选择渠道", trigger: "blur"}],
        bindAccountId: [{
          required: false, 
          validator: (rule, value, callback) => {
            if (this.form.channelType === 'wx_wkteam' && !value) {
              callback(new Error('请绑定账号'));
            } else {
              callback();
            }
          }, 
          trigger: "blur"
        }],
        host: [{required: true, message: "请选择主机", trigger: "blur"}],
      }
    }
  },
  methods: {
    async hostChange(val) {
      await api.getPort({'host': val}).then((result) => {
        if (result) {
          this.form.port = result
        }
      });
    },
    save() {
      // 在验证前给bindAccountName赋值
      if (this.form.bindAccountId) {
        const selectedAccount = this.dict.wechatAccountList.find(item => item.wcId === this.form.bindAccountId);
        if (selectedAccount) {
          this.form.bindAccountName = selectedAccount.nickName;
        }
      }

      this.$refs['addAppForm'].validate((valid) => {
        if (valid) {
          this.$emit('save', this.form)
        } else {
          return false
        }
      })
    },
    // 重置表单数据
    resetForm() {
      this.form = {
        applicationName: '',
        customer: '',
        bindAccountId: '',
        bindAccountName: '',
        channelType: '',
        host: '',
        port: ''
      }
      // 如果表单已经挂载，重置验证结果
      if (this.$refs.addAppForm) {
        this.$refs.addAppForm.resetFields()
      }
    },
    handleClose() {
      this.resetForm()
      this.$emit('close')
    }
  },
  props: {
    dict: {
      type: Object,
      default: () => {
      }
    },
    isUpdate: {
      type: Boolean,
      default: false
    },
    visibleProps: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: () => {
      }
    }
  },
  watch: {
    visibleProps: {
      handler: function (val) {
        this.visible = val
      },
      immediate: true
    },
    rowData: {
      handler: function (val) {
        if (val && Object.keys(val).length > 0) {
          this.form = {...val}  // 使用展开运算符复制对象
        }
      },
      immediate: true
    },
    isUpdate: {
      handler: function (val) {
        if (!val) {
          this.resetForm();
        }
      },
      immediate: true
    },
    'form.channelType': {
      handler: function(val) {
        // 当渠道类型变化时，重新校验绑定账号字段
        this.$nextTick(() => {
          if (this.$refs.addAppForm) {
            this.$refs.addAppForm.validateField('bindAccountId');
          }
        });
      }
    }
  },

}

</script>

<style scoped lang="scss">
</style>
