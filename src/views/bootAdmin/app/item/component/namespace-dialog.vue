<template>
  <el-dialog
    :title="isUpdate ? '编辑命名空间' : '新增命名空间'"
    :visible.sync="visible"
    @close="handleClose"
    close-on-press-escape
    :close-on-click-modal="false"
  >
    <el-form :model="form" ref="namespaceForm" label-position="right" label-width="100px" :rules="rules">
      <el-form-item label="命名空间" prop="name">
        <el-input v-model="form.name" clearable></el-input>
      </el-form-item>

      <el-form-item label="是否公共" prop="isPublic">
        <el-radio-group v-model="form.isPublic">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="save">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import api from "@/api";

export default {
  props: {
    visibleProps: {
      type: Boolean,
      default: false
    },
    isUpdate: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: () => ({})
    },
    appOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      visible: false,
      form: {
        name: '',
        appId: '',
        isPublic: 0
      },
      rules: {
        name: [
          { required: true, message: '请输入命名空间名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        appId: [
          { required: true, message: '请选择应用', trigger: 'change' }
        ],
        isPublic: [
          { required: true, message: '请选择是否公共', trigger: 'change' }
        ]
      }
    };
  },
  watch: {
    visibleProps: {
      handler(val) {
        this.visible = val;
        if (val) {
          this.initForm();
        }
      },
      immediate: true
    }
  },
  methods: {
    initForm() {
      if (this.isUpdate && this.rowData) {
        this.form = {
          id: this.rowData.id,
          name: this.rowData.name,
          appId: this.rowData.appId,
          isPublic: this.rowData.isPublic
        };
      } else {
        this.resetForm();
      }
    },
    resetForm() {
      this.form = {
        name: '',
        appId: '',
        isPublic: 0
      };
      // 如果表单已经挂载，重置验证结果
      if (this.$refs.namespaceForm) {
        this.$refs.namespaceForm.resetFields();
      }
    },
    handleClose() {
      this.resetForm();
      this.$emit('close');
    },
    save() {
      this.$refs.namespaceForm.validate(valid => {
        if (valid) {
          this.$emit('save', this.form);
        } else {
          return false;
        }
      });
    }
  }
};
</script>
