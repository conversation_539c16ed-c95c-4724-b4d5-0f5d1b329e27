<template>
  <d2-container class="page">
    <el-form ref="searchForm" :model="searchInfo" :inline="true" :label-position="position">
      <el-form-item>
        <el-select v-model="searchInfo.appId" placeholder="请选择应用" filterable clearable @change="searchChange(1)">
          <el-option
            v-for="item in appOptions"
            :key="item.id"
            :label="item.applicationName"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <el-row :gutter="2">
      <div class="avue-crud__menu">
        <div class="avue-crud__left">
          <el-button
            type="primary"
            @click="handleAddNamespace"
            :disabled="!searchInfo.appId"
            class="el-button filter-item el-button--success el-button--mini"
          >
            <span>新增命名空间</span>
          </el-button>
        </div>
      </div>
      <el-col :xs="24">
        <el-collapse v-model="activeNames" accordion>
          <el-collapse-item
            v-for="namespace in namespaceList"
            :key="namespace.id"
            :title="namespace.name"
            :name="namespace.id"
          >
            <div class="namespace-header">
              <div class="namespace-info">
                <el-tag :type="namespace.isPublic === 1 ? 'success' : 'info'" size="small">
                  {{ namespace.isPublic === 1 ? '公共' : '私有' }}
                </el-tag>
                <span class="namespace-app">应用ID: {{ namespace.appId }}</span>
              </div>
              <div class="namespace-actions">
                <el-button size="mini" type="primary" @click.stop="handleEditNamespace(namespace)">编辑</el-button>
                <el-button size="mini" type="danger" @click.stop="handleDeleteNamespace(namespace)">删除</el-button>
                <el-button size="mini" type="success" @click.stop="handleAddItem(namespace)">新增配置</el-button>
              </div>
            </div>

            <el-tabs v-model="namespaceViewModes[namespace.id]" class="namespace-tabs">
              <el-tab-pane label="表格模式" name="table"></el-tab-pane>
              <el-tab-pane label="文本模式" name="text"></el-tab-pane>
            </el-tabs>

            <!-- 表格模式 -->
            <div v-if="namespaceViewModes[namespace.id] === 'table'">
              <el-table
                :data="getConfigItems(namespace.id)"
                style="width: 100%"
                stripe
              >
                <el-table-column prop="itemKey" label="配置项Key" min-width="150"></el-table-column>
                <el-table-column prop="itemValue" label="配置项值" min-width="200">
                  <template slot-scope="scope">
                    <div class="value-cell">{{ scope.row.itemValue }}</div>
                  </template>
                </el-table-column>
                <el-table-column prop="comment" label="注释" min-width="150"></el-table-column>
                <el-table-column label="操作" width="150" fixed="right">
                  <template slot-scope="scope">
                    <el-button size="mini" @click.stop="handleEditItem(scope.row)">编辑</el-button>
                    <el-button size="mini" type="danger" @click.stop="handleDeleteItem(scope.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <!-- 文本模式 -->
            <div v-else-if="namespaceViewModes[namespace.id] === 'text'">
              <el-form>
                <el-form-item>
                  <div class="edit-container">
                    <div class="edit">
                      <div class="leftBox">
                        <textarea cols="2" :id="`leftNum-${namespace.id}`" disabled></textarea>
                      </div>
                      <textarea
                        @input="handleTextareaInput($event, namespace.id)"
                        v-model="namespaceTextConfigs[namespace.id]"
                        :onscroll="`document.getElementById('leftNum-${namespace.id}').scrollTop = this.scrollTop;`"
                        spellcheck="false"
                        class="area-content"></textarea>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" size="small" @click.stop="saveTextConfig(namespace)">保存配置</el-button>
                </el-form-item>
              </el-form>
            </div>
          </el-collapse-item>
        </el-collapse>
      </el-col>
    </el-row>

    <!-- 命名空间弹窗 -->
    <NamespaceDialog
      :visible-props="namespaceDialogVisible"
      :app-options="appOptions"
      :is-update="!!currentNamespace.id"
      @close="namespaceDialogVisible = false"
      @save="saveNamespace"
      :row-data="currentNamespace"
    ></NamespaceDialog>

    <!-- 配置项弹窗 -->
    <ItemDialog
      :visible-props="itemDialogVisible"
      :is-update="!!currentItem.id"
      @close="itemDialogVisible = false"
      @save="saveItem"
      :row-data="currentItem"
      :namespace-id="currentNamespaceId"
    ></ItemDialog>
  </d2-container>
</template>

<script>
import api from "@/api";
import NamespaceDialog from '@/views/bootAdmin/app/item/component/namespace-dialog.vue'
import ItemDialog from '@/views/bootAdmin/app/item/component/item-dialog.vue'
import {cloneDeep} from 'lodash'

export default {
  name: "appConfig",
  components: {
    NamespaceDialog,
    ItemDialog
  },
  data() {
    return {
      namespaceDialogVisible: false,
      itemDialogVisible: false,
      position: "left",
      namespaceList: [],
      configItems: [],
      activeNames: [],
      namespaceViewModes: {}, // 存储每个命名空间的视图模式
      namespaceTextConfigs: {}, // 存储每个命名空间的文本配置
      searchInfo: {
        pageSize: 10,
        pageNum: 1,
        total: 0,
        name: '',
        isPublic: '',
        sortColumn: ['create_time'],
        sortType: 'ASC'
      },
      currentNamespace: {},
      currentItem: {},
      currentNamespaceId: null,
      isPublicOptions: [
        {label: '是', value: 1},
        {label: '否', value: 0}
      ],
      appOptions: []
    };
  },
  methods: {
    getAppList() {
      api.appInfoList().then(result => {
        this.appOptions = result || [];
      });
    },
    // 命名空间相关方法
    handleAddNamespace() {
      this.currentNamespace = {};
      this.namespaceDialogVisible = true;
    },
    handleEditNamespace(namespace) {
      this.currentNamespace = cloneDeep(namespace);
      this.namespaceDialogVisible = true;
    },
    handleDeleteNamespace(namespace) {
      this.$confirm('确认删除该命名空间? 删除后将无法恢复，且会删除该命名空间下的所有配置项。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        api.appNamespaceDeleteById(namespace.id).then(response => {
          this.$message.success('删除成功');
          this.searchChange(1);
        })
      })
    },
    saveNamespace(val) {
      val.appId = this.searchInfo.appId
      // 根据是否存在id判断是新增还是修改
      const request = val.id
        ? api.appNamespaceUpdateById(val.id, val)  // 修改
        : api.appNamespaceSave(val);               // 新增

      request.then(() => {
        this.namespaceDialogVisible = false;
        this.$message.success(`${val.id ? '修改' : '保存'}成功`);
        this.searchChange(1);
        this.currentNamespace = {};  // 清空编辑数据
      });
    },

    // 配置项相关方法
    handleAddItem(namespace) {
      this.currentItem = {};
      this.currentNamespaceId = namespace.id;
      this.itemDialogVisible = true;
    },
    handleEditItem(item) {
      this.currentItem = cloneDeep(item);
      this.currentNamespaceId = item.nameId;
      this.itemDialogVisible = true;
    },
    handleDeleteItem(item) {
      this.$confirm('确认删除该配置项?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        api.appItemDeleteById(item.id).then(() => {
          this.$message.success('删除成功');
          this.getConfigItemList(this.searchInfo.appId);
        });
      }).catch(() => {
      });
    },
    saveItem(val) {
      const currentNamespaceItems = this.getConfigItems(val.nameId);
      if (val.id) {
        // 修改模式：排除当前项，检查是否有重复 key
        const isDuplicate = currentNamespaceItems.some(
          item => item.itemKey === val.itemKey && item.id !== val.id
        );
        if (isDuplicate) {
          this.$message.error(`配置项Key已存在: ${val.itemKey}`);
          return;
        }
        // 修改模式：获取当前key的sort值
        val.sort = currentNamespaceItems.find(
          item => item.id === val.id
        )?.sort;
      } else {
        // 新增模式：检查是否已有相同 key
        const isDuplicate = currentNamespaceItems.some(
          item => item.itemKey === val.itemKey
        );
        if (isDuplicate) {
          this.$message.error(`配置项Key已存在: ${val.itemKey}`);
          return;
        }
        val.sort = this.getConfigItems(val.nameId).length + 1
      }


      // 根据是否存在id判断是新增还是修改
      const request = val.id
        ? api.appItemUpdateById(val.id, val)  // 修改
        : api.appItemSave(val);               // 新增

      request.then(() => {
        this.itemDialogVisible = false;
        this.$message.success(`${val.id ? '修改' : '保存'}成功`);
        this.getConfigItemList(this.searchInfo.appId);
        this.currentItem = {};  // 清空编辑数据
      });
    },

    // 获取指定命名空间下的配置项
    getConfigItems(namespaceId) {
      return this.configItems.filter(item => item.nameId === namespaceId);
    },

    // 分页和查询相关方法
    handleSizeChange(pageSize) {
      this.searchInfo.pageSize = pageSize;
      this.getNamespacePage();
    },
    searchChange(pageNum) {
      this.searchInfo.pageNum = pageNum || 1;
      this.getNamespacePage();
    },
    // 初始化命名空间视图模式
    initNamespaceViewModes(namespaces) {
      namespaces.forEach(namespace => {
        if (!this.namespaceViewModes[namespace.id]) {
          this.$set(this.namespaceViewModes, namespace.id, 'table');
        }
      });
    },
    // 将配置项转换为文本格式
    convertItemsToText(namespaceId) {
      const items = this.getConfigItems(namespaceId);
      let text = '';
      items.forEach(item => {
        text += `${item.itemKey}=${item.itemValue}\n`;
      });
      this.$set(this.namespaceTextConfigs, namespaceId, text);
      return text;
    },
    // 保存文本配置
    saveTextConfig(namespace) {
      const text = this.namespaceTextConfigs[namespace.id];
      if (!text) {
        this.$message.warning('配置内容不能为空');
        return;
      }

      try {
        const lines = text.split('\n').filter(line => line.trim() !== '');
        const newItems = [];
        const existingItems = this.getConfigItems(namespace.id);
        const existingKeys = existingItems.map(item => item.itemKey);
        const newKeys = [];

        const seenKeys = new Set(); // 用于记录已出现的 key
        let index = 1;
        // 解析文本内容为配置项
        for (const line of lines) {
          const parts = line.split('=');
          if (parts.length < 2) {
            throw new Error(`无效的配置行: ${line}`);
          }

          const key = parts[0].trim();
          const value = parts.slice(1).join('=').trim();

          if (!key) {
            throw new Error('配置项Key不能为空');
          }

          console.log(seenKeys)
          if (seenKeys.has(key)) {
            throw new Error(`配置项Key重复: ${key}`);
          }
          seenKeys.add(key);

          newKeys.push(key);
          newItems.push({
            nameId: namespace.id,
            itemKey: key,
            itemValue: value,
            sort: index++  // 按当前顺序重新分配序号
          });
        }

        // 找出需要删除的项（旧配置中有但新配置中没有的key，或者key相同但value不同的项）
        const itemsToDelete = existingItems.filter(existingItem => {
          // 如果key不在新配置中，需要删除
          if (!newKeys.includes(existingItem.itemKey)) return true;

          // 如果key在新配置中，但value不同，也需要删除（后面会重新添加）
          const newItem = newItems.find(item => item.itemKey === existingItem.itemKey);
          return newItem && newItem.itemValue !== existingItem.itemValue;
        });

        // 找出需要新增的项（新配置中有但旧配置中没有的key，或者key相同但value不同的项）
        const itemsToAdd = newItems.filter(newItem => {
          // 如果key不在旧配置中，需要新增
          if (!existingKeys.includes(newItem.itemKey)) return true;

          // 如果key在旧配置中，但value不同，也需要新增（先删除旧的，再添加新的）
          const existingItem = existingItems.find(item => item.itemKey === newItem.itemKey);
          return existingItem && existingItem.itemValue !== newItem.itemValue;
        });

        // 执行删除操作
        const deletePromises = itemsToDelete.map(item => api.appItemDeleteById(item.id));

        Promise.all(deletePromises)
          .then(() => {
            // 执行新增操作
            const addPromises = itemsToAdd.map(item => api.appItemSave(item));
            return Promise.all(addPromises);
          })
          .then(() => {
            this.$message.success('保存成功');
            this.getConfigItemList(this.searchInfo.appId);
          })
          .catch(error => {
            this.$message.error('操作失败: ' + error.message);
          });
      } catch (error) {
        this.$message.error(error.message);
      }
    },
    getNamespacePage() {
      if (!this.searchInfo.appId) {
        return
      }
      let params = JSON.parse(JSON.stringify(this.searchInfo));
      api.appNamespaceListPage(params).then((result) => {
        this.namespaceList = result.records;
        this.searchInfo.total = Number(result.total);

        // 初始化命名空间视图模式
        this.initNamespaceViewModes(this.namespaceList);

        // 如果有命名空间，获取配置项
        if (this.namespaceList.length > 0) {
          this.getConfigItemList(this.searchInfo.appId);
        }
      });
    },
    getConfigItemList(appId) {
      api.appItemList({'appId': appId}).then((result) => {
        this.configItems = result || [];

        // 更新文本配置
        this.namespaceList.forEach(namespace => {
          this.convertItemsToText(namespace.id);
        });
      });
    },
    handleTextareaInput(event, namespaceId) {
      const text = this.namespaceTextConfigs[namespaceId] || '';
      const lineCount = text.split('\n').length;
      const lineNumbers = Array.from({ length: lineCount }, (_, i) => i + 1).join('\n');

      this.$nextTick(() => {
        const leftNumTextarea = document.getElementById(`leftNum-${namespaceId}`);
        if (leftNumTextarea) {
          leftNumTextarea.value = lineNumbers;
        }
      });
    },
    // 初始化所有命名空间的行号
    initAllLineNumbers() {
      if (this.namespaceList && this.namespaceList.length > 0) {
        this.namespaceList.forEach(namespace => {
          if (this.namespaceTextConfigs[namespace.id]) {
            this.handleTextareaInput(null, namespace.id);
          }
        });
      }
    }
  },
  mounted() {
    this.getAppList();
    this.$nextTick(() => {
      this.initAllLineNumbers();
    });
  },
  updated() {
    this.$nextTick(() => {
      this.initAllLineNumbers();
    });
  }
};
</script>

<style scoped lang="scss">
.namespace-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;

  .namespace-info {
    display: flex;
    align-items: center;

    .namespace-app {
      margin-left: 10px;
      color: #606266;
      font-size: 13px;
    }
  }
}

.namespace-tabs {
  margin-bottom: 15px;
}

.value-cell {
  word-break: break-word;
  white-space: pre-wrap;
  max-height: 100px;
  overflow-y: auto;
}

.edit-container {
  width: 100%;
  height: 300px;

  .edit {
    display: flex;
    height: 100%;
    border: 1px solid #dcdfe6;
    border-radius: 4px;

    .leftBox {
      width: 40px;
      min-width: 40px; /* 添加最小宽度，防止被挤压 */
      height: 100%;
      background-color: #f5f7fa;
      border-right: 1px solid #dcdfe6;
      flex-shrink: 0; /* 防止被挤压 */

      textarea {
        width: 40px;
        box-sizing: border-box;
        height: 100%;
        border: none;
        resize: none;
        text-align: right;
        padding: 5px;
        background-color: rgb(238, 238, 238, 1);
        color: #888888;
        font-size: 12px;
        line-height: 21px;
        overflow-y: hidden; /* 隐藏垂直滚动条 */
      }
    }

    .area-content {
      flex: 1;
      border: none;
      resize: none;
      padding: 5px;
      font-family: monospace;
      line-height: 21px;
      outline: none;
      background-color: rgb(238, 238, 238, 1);
      color: rgb(127, 0, 85, 1);
    }
  }
}

#leftNum {
  padding: 10px 4px;
  height: 100%;
  width: 100%;
  line-height: 24px;
  font-size: 13px;
  text-align: right;
  color: #fff;
  font-weight: bold;
  resize: none;
  outline: none;
  border: 0;
  background: black;
  box-sizing: border-box;
}

</style>
