<template>
  <el-dialog
    :visible.sync="visible"
    width="600px"
    class="qa-add-dialog"
    :close-on-click-modal="false"
    :before-close="handleClose"
  >
    <template #title>
      <div>
        <span class="qa-add-title"><i class="el-icon-plus"></i> {{ $t('knowledge.qa.dialog.add.title') }}</span>
        <div class="qa-add-desc">{{ $t('knowledge.qa.dialog.add.desc') }}</div>
      </div>
    </template>
    <el-form :model="form" :rules="rules" ref="form" label-position="top">
      <el-form-item :label="$t('knowledge.qa.dialog.add.question')" prop="question" required>
        <el-input
          type="textarea"
          :rows="3"
          v-model="form.question"
          :placeholder="$t('knowledge.qa.dialog.add.placeholder.question')"
        />
      </el-form-item>
      <el-form-item :label="$t('knowledge.qa.dialog.add.answer')" prop="answer" required>
        <el-input
          type="textarea"
          :rows="3"
          v-model="form.answer"
          :placeholder="$t('knowledge.qa.dialog.add.placeholder.answer')"
        />
      </el-form-item>
<!--      <el-form-item :label="$t('knowledge.qa.dialog.add.tags')" prop="tags">-->
<!--        <el-input-->
<!--          v-model="form.tags"-->
<!--          :placeholder="$t('knowledge.qa.dialog.add.placeholder.tags')"-->
<!--        />-->
<!--        <div class="qa-add-tip">{{ $t('knowledge.qa.dialog.add.tips.tags') }}</div>-->
<!--      </el-form-item>-->
      <el-form-item :label="$t('knowledge.qa.dialog.add.source')" prop="source">
        <el-select v-if="!showNewSource" v-model="form.source" :placeholder="$t('knowledge.qa.dialog.add.placeholder.source')">
          <el-option
            v-for="item in sourceOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-input
          v-if="showNewSource"
          v-model="form.newSource"
          :placeholder="$t('knowledge.qa.dialog.add.newSource')"
        />
        <el-button type="text" @click="toggleSourceMode" class="qa-add-tip">
          {{ showNewSource ? $t('knowledge.qa.dialog.add.sourceToggle.select') : $t('knowledge.qa.dialog.add.sourceToggle.create') }}
        </el-button>
      </el-form-item>
    </el-form>
    <div class="qa-add-footer">
      <el-button class="filter-item el-button--small" @click="handleClose">{{ $t('knowledge.qa.dialog.add.buttons.cancel') }}</el-button>
      <template v-if="!isEdit">
        <el-button class="filter-item el-button--small" :loading="saveLoading" @click="handleSave(true)">{{ $t('knowledge.qa.dialog.add.buttons.saveAndContinue') }}</el-button>
        <el-button class="filter-item el-button--small" :loading="saveLoading" type="primary" @click="handleSave(false)">{{ $t('knowledge.qa.dialog.add.buttons.add') }}</el-button>
      </template>
      <template v-if="isEdit">
        <el-button class="filter-item el-button--small" :loading="saveLoading" type="primary" @click="handleSave(false)">{{ $t('knowledge.qa.dialog.add.buttons.confirm') }}</el-button>
      </template>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'QaAddDialog',
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    sourceOptions: {
      type: Array,
      default: () => []
    },
    initialData: {
      type: Object,
      default: null
    }
  },
  data() {
    const validateSource = (rule, value, callback) => {
      if (this.showNewSource) {
        if (!this.form.newSource) {
          callback(new Error(this.$t('knowledge.qa.dialog.add.validation.newSource')));
        } else {
          callback();
        }
      } else {
        if (!this.form.source) {
          callback(new Error(this.$t('knowledge.qa.dialog.add.validation.source')));
        } else {
          callback();
        }
      }
    };

    return {
      form: {
        question: '',
        answer: '',
        tags: '',
        source: '',
        newSource: '',
        documentId: null
      },
      rules: {
        question: [{ required: true, message: this.$t('knowledge.qa.dialog.add.validation.question'), trigger: 'blur' }],
        answer: [{ required: true, message: this.$t('knowledge.qa.dialog.add.validation.answer'), trigger: 'blur' }],
        source: [{ validator: validateSource, trigger: 'change' }]
      },
      showNewSource: false,
      saveLoading: false
    }
  },
  computed: {
    isEdit() {
      return !!this.initialData;
    }
  },
  watch: {
    visible(val) {
      if (val) {
        if (this.isEdit) {
          this.fillFormFromInitial();
        } else {
          this.resetForm();
        }
      }
    }
  },
  methods: {
    fillFormFromInitial() {
      const data = this.initialData || {};
      this.form.question = data.question || '';
      this.form.answer = data.answer || '';
      this.form.tags = (data.tags || []).join(',');
      this.form.source = data.source || '';
      this.form.newSource = '';
      this.form.documentId = data.documentId || null;
      this.showNewSource = false;
      // 重置验证状态
      if (this.$refs.form) this.$refs.form.clearValidate();
    },
    resetForm() {
      this.form = {
        question: '',
        answer: '',
        tags: '',
        source: '',
        newSource: '',
        documentId: null
      };
      this.showNewSource = false;
      if (this.$refs.form) {
        this.$refs.form.resetFields();
      }
    },
    handleClose() {
      this.$emit('update:visible', false)
      this.$emit('close')
    },
    handleSave(keepOpen) {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.saveLoading = true;

          const data = {
            ...this.form,
            tags: this.form.tags.split(',').map(t => t.trim()).filter(Boolean),
            source: this.showNewSource ? this.form.newSource : this.form.source,
            documentId: this.form.documentId
          }

          this.$emit('save', data, keepOpen)

          setTimeout(() => {
            this.saveLoading = false;

            if (!keepOpen) {
              this.handleClose()
            } else {
              if (this.isEdit) {
                this.fillFormFromInitial();
              } else {
                this.resetForm();
              }
            }
          }, 500);
        }
      })
    },
    toggleSourceMode() {
      this.showNewSource = !this.showNewSource;
      if (this.showNewSource) {
        this.form.source = '';
      } else {
        this.form.newSource = '';
      }
      this.$nextTick(() => {
        this.$refs.form.validateField('source');
      });
    }
  }
}
</script>

<style scoped lang="scss">
.qa-add-dialog {
  .qa-add-title {
    font-size: 16px;
    font-weight: bold;
    display: flex;
    align-items: center;
    i {
      margin-right: 6px;
    }
  }
  .qa-add-desc {
    font-size: 12px;
    color: #888fa1;
  }
  .el-form {
    .el-form-item {
      .el-select {
        width: 100%;
        display: block;
      }
    }
  }
  .qa-add-tip {
    color: #888fa1;
    font-size: 12px;
  }
  .qa-add-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 18px;
    .el-button {
      min-width: 90px;
    }
  }

  // 调整dialog body的上边距
  ::v-deep .el-dialog__body {
    margin-top: -30px;
  }
  ::v-deep .el-form-item__label{
    margin-bottom: -18px
  }
}
</style>
