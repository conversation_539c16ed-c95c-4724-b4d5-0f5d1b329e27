<template>
  <el-dialog
    :visible.sync="visible"
    width="500px"
    class="qa-batch-import-dialog"
    :close-on-click-modal="false"
    :before-close="handleClose"
  >
    <template #title>
      <div>
        <span class="qa-batch-title"><i class="el-icon-upload2"></i> {{ $t('knowledge.qa.dialog.batchImport.title') }}</span>
        <div class="qa-batch-desc">{{ $t('knowledge.qa.dialog.batchImport.desc') }}</div>
      </div>
    </template>

    <div class="csv-format-section">
      <div class="csv-header">
        <i class="el-icon-document"></i> {{ $t('knowledge.qa.dialog.batchImport.format.title') }}
      </div>
      <div class="csv-desc-row">
        <span class="csv-desc">{{ $t('knowledge.qa.dialog.batchImport.format.desc') }}</span>
        <el-button style="margin-left:50px" class="filter-item el-button--mini" icon="el-icon-download" @click="handleDownloadTemplate">{{ $t('knowledge.qa.dialog.batchImport.format.download') }}</el-button>
      </div>
    </div>

    <div class="file-upload-section">
      <div class="file-upload-label">{{ $t('knowledge.qa.dialog.batchImport.upload.label') }} <span class="required">*</span></div>
      <div class="file-upload-area">
        <el-upload
          class="upload-demo"
          ref="upload"
          action=""
          :http-request="handleUpload"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          :file-list="fileList"
          :multiple="false"
          :auto-upload="false"
          :limit="1"
          accept=".xlsx">
          <div class="upload-button-container">
            <el-button type="text" size="mini" plain>{{ $t('knowledge.qa.dialog.batchImport.upload.button') }}</el-button>
            <span class="file-status-text">{{fileStatus}}</span>
            <el-button v-if="fileList.length > 0" type="text" size="mini" @click.stop="clearFiles" style="margin-left: 10px;">{{ $t('knowledge.qa.dialog.batchImport.upload.clear') }}</el-button>
          </div>
        </el-upload>
      </div>
    </div>

    <div class="file-source-section" v-if="hasFile">
      <div class="file-source-label">{{ $t('knowledge.qa.dialog.batchImport.source.label') }} <span class="required">*</span></div>
      <div class="file-source-area">
        <el-select v-if="!showNewSource" v-model="form.source" :placeholder="$t('knowledge.qa.dialog.add.placeholder.source')">
          <el-option
            v-for="item in sourceOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-input
          v-if="showNewSource"
          v-model="form.newSource"
          :placeholder="$t('knowledge.qa.dialog.add.newSource')"
        />
        <el-button type="text" @click="toggleSourceMode" class="source-toggle-btn">
          {{ showNewSource ? $t('knowledge.qa.dialog.add.sourceToggle.select') : $t('knowledge.qa.dialog.add.sourceToggle.create') }}
        </el-button>
      </div>
    </div>

    <div class="qa-batch-footer">
      <el-button class="filter-item el-button--small" @click="handleClose">{{ $t('knowledge.qa.dialog.batchImport.buttons.cancel') }}</el-button>
      <el-button class="filter-item el-button--small" type="primary" @click="submitUpload" :disabled="!canSubmit" :loading="loading">{{ $t('knowledge.qa.dialog.batchImport.buttons.upload') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import api from "@/api";

export default {
  name: 'QaBatchImportDialog',
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    sourceOptions: {
      type: Array,
      default: () => []
    },
    deptId: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      fileList: [],
      fileStatus: this.$t('knowledge.qa.dialog.batchImport.upload.status.none'),
      showNewSource: false,
      form: {
        source: '',
        newSource: ''
      },
      loading: false
    }
  },
  computed: {
    hasFile() {
      return this.fileList.length > 0;
    },
    canSubmit() {
      return this.hasFile && (
        (this.showNewSource && this.form.newSource) ||
        (!this.showNewSource && this.form.source)
      );
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.resetForm();
      } else {
        this.resetUpload();
      }
    }
  },
  methods: {
    resetForm() {
      this.fileList = [];
      this.fileStatus = this.$t('knowledge.qa.dialog.batchImport.upload.status.none');
      this.form.source = '';
      this.form.newSource = '';
      this.showNewSource = false;
    },
    handleClose() {
      this.$emit('update:visible', false);
      this.$emit('close');
      this.resetForm();
    },
    handleDownloadTemplate() {
      api.templateDownload();
    },
    resetUpload() {
      // 重置上传组件
      if (this.$refs.upload) {
        this.$refs.upload.clearFiles();
      }
      this.fileList = [];
      this.fileStatus = this.$t('knowledge.qa.dialog.batchImport.upload.status.none');
    },
    handleFileChange(file, fileList) {
      // 文件大小限制：10MB
      const maxSize = 10 * 1024 * 1024; // 10MB
      
      // 检查文件大小是否超过限制
      if (file.size > maxSize) {
        this.$message.error(this.$t('knowledge.file.fileSizeExceeded', {maxSize: '10MB'}));
        this.resetUpload();
        return;
      }
      
      // 只保留最新选择的文件
      this.$nextTick(() => {
        // 如果用户选择了新文件，我们只保留新文件
        if (fileList.length > 0) {
          // 使用直接赋值而不是调用resetUpload避免无限循环
          this.fileList = [fileList[fileList.length - 1]];
          this.fileStatus = this.$t('knowledge.qa.dialog.batchImport.upload.status.selected', { filename: this.fileList[0].name });
        } else {
          this.fileList = [];
          this.fileStatus = this.$t('knowledge.qa.dialog.batchImport.upload.status.none');
        }
      });
    },
    handleFileRemove() {
      this.fileList = [];
      this.fileStatus = this.$t('knowledge.qa.dialog.batchImport.upload.status.none');
    },
    clearFiles() {
      this.resetUpload();
    },
    toggleSourceMode() {
      this.showNewSource = !this.showNewSource;
      if (this.showNewSource) {
        this.form.source = '';
      } else {
        this.form.newSource = '';
      }
    },
    submitUpload() {
      if (this.fileList.length === 0) {
        this.$message.warning(this.$t('knowledge.qa.dialog.batchImport.messages.selectFile'));
        return;
      }

      const fileName = this.showNewSource ? this.form.newSource : this.form.source;
      if (!fileName) {
        this.$message.warning(this.$t('knowledge.qa.dialog.batchImport.messages.selectSource'));
        return;
      }

      this.$refs.upload.submit();
    },
    async handleUpload(options) {
      const { file } = options;
      const formData = new FormData();
      formData.append('file', file);

      // 添加fileName参数
      const fileName = this.showNewSource ? this.form.newSource : this.form.source;
      formData.append('fileName', fileName);
      
      // 添加deptId参数
      if (this.deptId) {
        formData.append('deptId', this.deptId);
      }

      this.loading = true;
      await api.uploadQa(formData, {
        'Content-Type': 'multipart/form-data'
      }).then(res => {
        this.$message.success(this.$t('knowledge.qa.dialog.batchImport.messages.success'));
        this.handleClose();
        this.$emit('success');
      }).catch(err => {
        console.error('Upload error:', err);
      }).finally(() => {
        this.loading = false;
      });
    }
  }
}
</script>

<style scoped lang="scss">
.qa-batch-import-dialog {
  .qa-batch-title {
    font-size: 16px;
    font-weight: bold;
    display: flex;
    align-items: center;
    i {
      margin-right: 6px;
    }
  }
  .qa-batch-desc {
    font-size: 12px;
    color: #888fa1;
  }

  .csv-format-section {
    border: 1px solid #EBEEF5;
    border-radius: 4px;
    padding: 16px;
    margin-bottom: 20px;

    .csv-header {
      display: flex;
      align-items: center;
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 8px;

      i {
        margin-right: 6px;
        color: #606266;
      }
    }

    .csv-desc-row {
      display: flex;
      align-items: center;
      font-size: 12px;
      color: #606266;
      margin-bottom: 12px;

      .csv-desc {
        margin-right: 12px;
      }
    }
  }

  .file-upload-section {
    margin-bottom: 20px;

    .file-upload-label {
      font-size: 14px;
      margin-bottom: 8px;

      .required {
        color: #F56C6C;
      }
    }

    .file-upload-area {
      border: 1px dashed #d9d9d9;
      border-radius: 4px;
      padding: 16px;

      .el-upload__tip {
        margin-top: 8px;
        color: #606266;
      }

      .upload-button-container {
        display: flex;
        align-items: center;

        .file-status-text {
          margin-left: 10px;
          color: #606266;
          font-size: 12px;
        }
      }
    }
  }

  .file-source-section {
    margin-bottom: 20px;

    .file-source-label {
      font-size: 14px;
      margin-bottom: 8px;

      .required {
        color: #F56C6C;
      }
    }

    .file-source-area {
      .el-select {
        width: 100%;
        display: block;
      }

      .source-toggle-btn {
        margin-top: 8px;
        color: #888fa1;
        font-size: 12px;
      }
    }
  }

  .qa-batch-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 18px;

    .el-button {
      min-width: 90px;
    }
  }

  // 调整dialog body的上边距
  ::v-deep .el-dialog__body {
    margin-top: -30px;
  }
}
</style>
