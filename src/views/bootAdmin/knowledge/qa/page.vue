<template>
  <d2-container class="qa-page">
    <!-- 顶部标题区域 -->
    <div class="qa-header">
<!--      <div class="qa-title-container">-->
<!--        <div class="qa-icon">-->
<!--          <i class="el-icon-notebook-1"></i>-->
<!--        </div>-->
<!--        <div class="qa-title-content">-->
<!--          <h2 class="qa-title">{{ $t('knowledge.qa.title') }}</h2>-->
<!--          <p class="qa-subtitle">{{ $t('knowledge.qa.subtitle') }}</p>-->
<!--        </div>-->
<!--      </div>-->
      <!-- 操作区 -->
      <div class="qa-actions-row">
        <div class="qa-search-center">
          <div class="custom-search-wrapper">
            <!-- 添加客户下拉框，仅超级管理员可见 -->
            <el-select
              v-if="isAdmin"
              v-model="searchForm.deptId"
              class="customer-select"
              filterable
              clearable
              :placeholder="$t('knowledge.qa.search.deptPlaceholder')"
              @change="handleSearch"
            >
              <el-option
                v-for="item in deptOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
            <el-input
              :placeholder="$t('knowledge.qa.search.placeholder')"
              v-model="searchForm.keyword"
              class="search-input-long"
              @keyup.enter.native="handleSearch"
            ></el-input>
            <el-button class="search-btn filter-item el-button--small" icon="el-icon-search"
                       @click="handleSearch">{{ $t('knowledge.qa.search.button') }}
            </el-button>
          </div>
        </div>
        <div class="qa-buttons-right">
          <el-button class="filter-item el-button--mini" type="primary" icon="el-icon-plus" :loading="loading"
                     @click="handleAdd">{{ $t('knowledge.qa.actions.add') }}
          </el-button>
          <el-button class="filter-item el-button--mini" type="primary" icon="el-icon-upload2" :loading="loading"
                     @click="handleBatchImport">{{ $t('knowledge.qa.actions.batch') }}
          </el-button>
        </div>
      </div>
    </div>

    <!-- 记录统计和筛选区域 -->
    <div class="center-area">
      <div class="qa-stat-filter">
        <div class="qa-stat">
          {{ $t('knowledge.qa.stats.total', {
            total: total,
            current: currentPage,
            totalPages: Math.ceil(total / pageSize),
            start: (currentPage - 1) * pageSize + 1,
            end: Math.min(currentPage * pageSize, total)
          }) }}
        </div>
        <div class="qa-filter">
          <span v-if="searchForm.source" class="filter-text">{{ $t('knowledge.qa.filter.text', { source: searchForm.source }) }}</span>
          <el-dropdown @command="handleSourceFilter">
            <el-button class="el-button filter-item el-button--primary el-button--mini" type="primary">
              {{ $t('knowledge.qa.filter.title') }}<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu>
              <el-dropdown-item command="">{{ $t('knowledge.qa.filter.allSources') }}</el-dropdown-item>
              <el-dropdown-item v-for="source in sourceOptions" :key="source.value" :command="source.value">
                {{ source.label }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
    </div>

    <!-- QA列表区域 -->
    <div class="qa-list-container" v-loading="loading">
      <div class="qa-item" v-for="(item, index) in qaList" :key="index">
        <el-card shadow="hover" class="qa-card">
          <div class="qa-content" @dblclick.stop="handleEdit(item)">
            <div class="qa-question">
              <h3>{{ item.question }}</h3>
            </div>
            <div class="qa-answer">
              {{ item.answer }}
            </div>
            <div class="qa-footer">
              <div class="qa-tags">
                <el-tag
                  v-for="tag in item.tags"
                  :key="tag"
                  size="small"
                  class="qa-tag"
                  @click="handleTagClick(tag)"
                >{{ tag }}
                </el-tag>
              </div>
              <div class="qa-source">{{ item.source }}</div>
              <div class="qa-actions">
                <el-button type="text" icon="el-icon-edit" :loading="loading" @click="handleEdit(item)"
                           :title="$t('knowledge.qa.actions.edit')"></el-button>
                <el-button type="text" icon="el-icon-delete" :loading="loading" @click="handleDelete(item)"
                           :title="$t('knowledge.qa.actions.delete')"></el-button>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>

    <qa-add-dialog
      :visible.sync="dialogVisible"
      :source-options="sourceOptions"
      :initial-data="editData"
      @save="handleSave"
      @close="handleDialogClose"
    />

    <qa-batch-import-dialog
      :visible.sync="batchImportDialogVisible"
      :source-options="sourceOptions"
      :dept-id="searchForm.deptId"
      @success="handleBatchImportSuccess"
    />
  </d2-container>
</template>

<script>
import QaAddDialog from './component/QaAddDialog.vue'
import QaBatchImportDialog from './component/QaBatchImportDialog.vue'
import api from "@/api";
import {mapActions} from "vuex";

export default {
  name: "qa",
  components: {
    QaAddDialog,
    QaBatchImportDialog
  },
  data() {
    return {
      searchForm: {
        keyword: '',
        source: '',
        deptId: ''
      },
      sourceOptions: [],
      deptOptions: [],
      isAdmin: false,
      qaList: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      dialogVisible: false,
      batchImportDialogVisible: false,
      editData: null,
      isEdit: false,
      loading: false
    }
  },
  created() {
    this.fetchData();
    this.getCurrentUserInfo();
  },
  methods: {
    ...mapActions("bootAdmin/user", [
      "currentUserInfo",
    ]),
    getCurrentUserInfo() {
      let _self = this;
      _self.currentUserInfo().then((result) => {
        this.isAdmin = result.isAdmin;
        if (this.isAdmin) {
          this.getDeptList();
          // 初始加载空deptId的文件列表
          this.getFileNameList('');
        }
      });
    },
    getDeptList() {
      api.DEPT_ALL_LIST().then(res => {
        this.deptOptions = res || [];
      });
    },
    getFileNameList(deptId) {
      api.getFileName({ deptId: deptId }).then(res => {
        this.sourceOptions = res.map(fileName => ({
          value: fileName,
          label: fileName
        }));
      });
    },
    fetchData() {
      this.loading = true;
      const params = {
        text: this.searchForm.keyword,
        source: this.searchForm.source,
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        deptId: this.searchForm.deptId
      };
      api.qaListPage(params).then(res => {
        this.qaList = res.records || [];
        this.total = Number(res.total) || 0;
      }).finally(() => {
        this.loading = false;
        this.getFileNameList(this.searchForm.deptId);
      });
    },
    handleSearch() {
      this.currentPage = 1;
      this.fetchData();
    },
    handleSourceFilter(source) {
      this.searchForm.source = source;
      this.currentPage = 1;
      this.fetchData();
    },
    handleTagClick(tag) {
      console.log('点击标签', tag);
      this.searchForm.keyword = tag;
      this.handleSearch();
    },
    handleAdd() {
      this.editData = null;
      this.isEdit = false;
      this.dialogVisible = true;
    },
    handleEdit(item) {
      this.editData = {...item};
      this.isEdit = true;
      this.dialogVisible = true;
    },
    handleDialogClose() {
      this.editData = null;
      this.isEdit = false;
    },
    handleBatchImport() {
      // 显示批量导入对话框
      this.batchImportDialogVisible = true;
    },
    handleBatchImportSuccess() {
      // 批量导入成功后刷新数据
      this.fetchData();
    },
    handleDelete(item) {
      this.$confirm(this.$t('knowledge.qa.dialog.delete.message'), this.$t('knowledge.qa.dialog.delete.title'), {
        confirmButtonText: this.$t('knowledge.qa.dialog.delete.confirm'),
        cancelButtonText: this.$t('knowledge.qa.dialog.delete.cancel'),
        type: 'warning'
      }).then(() => {
        // 用户点击了确认按钮，开始加载状态
        this.loading = true;
        const params = {
          documentId: item.documentId,
          fileName: item.source,
          deptId: this.searchForm.deptId
        };
        api.deleteQa(params).then(res => {
          this.$message.success(this.$t('knowledge.qa.dialog.delete.success'));
        }).finally(() => {
          this.fetchData();
          this.loading = false;
        });
      }).catch(() => {
        // 用户点击了取消按钮
        this.$message.info(this.$t('knowledge.qa.dialog.delete.canceled'));
      });
    },
    handleSizeChange(size) {
      this.pageSize = size;
      this.fetchData();
    },
    handleCurrentChange(page) {
      this.currentPage = page;
      this.fetchData();
    },
    async handleSave(data, keepOpen) {
      this.loading = true;
      const param = {
        question: data.question,
        answer: data.answer,
        tags: data.tags,
        fileName: data.source,
        deptId: this.searchForm.deptId
      };

      if (data.documentId) {
        param.documentId = data.documentId;

        if (this.editData && this.editData.source) {
          param.oldFileName = this.editData.source;
        }
      }

      await api.qaSave(param).then(res => {
        this.$message.success(data.documentId ? this.$t('knowledge.qa.messages.editSuccess') : this.$t('knowledge.qa.messages.addSuccess'));
        this.fetchData();

        if (!keepOpen) {
          this.dialogVisible = false;
        }
        this.loading = false;
      }).catch(err => {
        this.loading = false;
      });
    }
  }
}
</script>

<style scoped lang="scss">
.qa-page {
  padding: 0;

  .qa-header {
    background: #fff;
    border-radius: 4px;
    padding: 16px 20px;
    margin-bottom: 16px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

    .qa-title-container {
      display: flex;
      align-items: center;
      flex: 0 0 auto;
      min-width: 150px;
      margin-right: 16px;
      margin-bottom: 10px;

      .qa-icon {
        width: 40px;
        height: 40px;
        border-radius: 6px;
        background: #f2f6fc;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        color: #606266;
        font-size: 20px;
        flex-shrink: 0;
      }

      .qa-title-content {
        .qa-title {
          font-size: 15px;
          color: #303133;
          margin: 0 0 4px 0;
          line-height: 1.2;
          white-space: nowrap;
        }

        .qa-subtitle {
          font-size: 10px;
          color: #909399;
          margin: 0;
          white-space: nowrap;
        }
      }
    }

    // 新增操作区布局
    .qa-actions-row {
      flex: 1 1 auto;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: center;
      position: relative;
      min-width: 0;
      gap: 10px;

      .qa-search-center {
        flex: 1 1 auto;
        display: flex;
        justify-content: center;
        align-items: center;
        min-width: 200px;
        max-width: 700px;
        margin: 0 auto 10px;

        .custom-search-wrapper {
          display: flex;
          width: 100%;
          max-width: 600px;
          flex-wrap: nowrap;

          .customer-select {
            width: 160px;
            margin-right: 10px;
            flex-shrink: 0;

            ::v-deep .el-input__inner {
              border-top-right-radius: 4px;
              border-bottom-right-radius: 4px;
              border: 2px solid #DCDFE6;
            }

            ::v-deep .el-input.is-focus .el-input__inner {
              border-color: #0d47a1;
            }
          }

          .search-input-long {
            flex: 1;
            min-width: 120px;

            ::v-deep .el-input__inner {
              border-right: none;
              border-top-right-radius: 0;
              border-bottom-right-radius: 0;
              border-top: 1px solid #DCDFE6;
              border-left: 1px solid #DCDFE6;
              &:focus {
                border: 2px solid;
                border-color: #0d47a1
              }
            }
          }

          .search-btn {
            height: 36px;
            padding: 0 12px;
            margin: 0;
            border-radius: 0 4px 4px 0;
            font-size: 12px;
            border: 1px solid #DCDFE6;
            border-left: none;
            flex-shrink: 0;
          }
        }
      }

      .search-input-long {
        min-width: 120px;
        max-width: 700px;
      }

      .qa-buttons-right {
        display: flex;
        gap: 8px;
        flex: 0 0 auto;
        margin-bottom: 10px;
      }
    }
  }

  .center-area {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;

    .qa-stat-filter {
      width: 100%;
      max-width: 800px;
      min-width: 320px;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      margin-bottom: 20px;
      gap: 10px;

      .qa-stat {
        color: #606266;
        font-size: 12px;
        flex: 1 1 auto;
        text-align: left;
        min-width: 200px;
      }

      .qa-filter {
        margin-left: 0;
        display: flex;
        align-items: center;
        position: relative;
        flex: 0 0 auto;

        .filter-text {
          color: #606266;
          font-size: 12px;
          margin-right: 10px;
          display: inline-block;
          white-space: nowrap;
        }

        .el-dropdown-link {
          color: #409EFF;
          cursor: pointer;
          font-size: 14px;
          display: flex;
          align-items: center;
        }
      }
    }
  }

  .qa-list-container {
    display: flex;
    flex-direction: column;
    align-items: center;

    .qa-item {
      margin-bottom: 16px;
      margin-top: 0;

      &:first-child {
        margin-top: 0;
      }

      width: 100%;
      display: flex;
      justify-content: center;

      .qa-card {
        border-left: 4px solid #0d47a1;
        width: 100%;
        max-width: 800px;
        min-width: 300px;
        border-radius: 12px;
        cursor: pointer;

        ::v-deep .el-card__body {
          padding: 10px 24px 12px 24px !important;
        }
        
        .qa-content {
          width: 100%;
        }

        .qa-footer {
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          gap: 10px;

          .qa-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            flex: 0 1 auto;
            min-width: 100px;

            .qa-tag {
              cursor: pointer;
            }
          }

          .qa-source {
            color: #909399;
            font-size: 12px;
            margin-left: 8px;
            flex: 0 0 auto;
          }

          .qa-actions {
            margin-left: auto;
            display: none;
            flex: 0 0 auto;

            .el-button {
              padding: 0;
              margin-left: 8px;
              font-size: 17px;
            }
          }
        }

        &:hover .qa-footer .qa-actions {
          display: flex;
        }

        .qa-question {
          margin-bottom: 12px;

          h3 {
            margin: 0;
            font-size: 14px;
            color: #303133;
            font-weight: 600;
            word-break: break-word;
          }
        }

        .qa-answer {
          color: #606266;
          font-size: 12px;
          line-height: 1.6;
          margin-bottom: 16px;
          word-break: break-word;
        }
      }
    }
  }

  .pagination-container {
    margin-top: 24px;
    display: flex;
    justify-content: center;

    ::v-deep .el-pagination,
    ::v-deep .el-pagination__total,
    ::v-deep .el-pagination__sizes,
    ::v-deep .el-pagination__jump,
    ::v-deep .el-pagination__editor,
    ::v-deep .el-pager,
    ::v-deep .el-pager li {
      font-size: 12px !important;
    }

    ::v-deep .el-pagination__sizes .el-input__inner {
      font-size: 12px !important;
      height: 28px;
      line-height: 28px;
      padding: 0 20px 0 8px;
    }
  }
}
</style>
