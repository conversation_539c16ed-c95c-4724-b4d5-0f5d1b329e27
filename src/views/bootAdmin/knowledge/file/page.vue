<template>
  <d2-container class="file-page">
    <div class="page-content-wrapper">
      <!-- 顶部区域 -->
      <div class="file-header">
        <div class="file-title-container">
<!--          <h2 class="file-title">{{$t('knowledge.file.title')}}</h2>-->
          <p class="file-subtitle">{{$t('knowledge.file.subtitle')}}</p>
        </div>

        <!-- 搜索和操作区 -->
        <div class="search-and-actions">
          <div class="search-box">
            <div class="custom-search-wrapper">
              <!-- 添加客户下拉框，仅超级管理员可见 -->
              <el-select
                v-if="isAdmin && !isRecycleBin"
                v-model="searchForm.deptId"
                class="customer-select"
                filterable
                clearable
                :placeholder="$t('knowledge.qa.search.deptPlaceholder')"
                @change="handleSearch"
              >
                <el-option
                  v-for="item in deptOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
              <el-input
                :placeholder="$t('knowledge.file.searchPlaceholder')"
                v-model="searchForm.fileName"
                prefix-icon="el-icon-search"
                class="search-input-long"
                @keyup.enter.native="handleSearch">
              </el-input>
            </div>
          </div>
          <div class="action-buttons">
            <!-- 根据状态显示不同的按钮 -->
            <template v-if="!isRecycleBin">
              <el-button class="filter-item el-button--mini" type="primary" icon="el-icon-upload" @click="handleUpload">
                {{$t('knowledge.file.upload')}}
              </el-button>
              <el-button class="filter-item el-button--mini" icon="el-icon-delete" @click="goToRecycleBin">{{$t('knowledge.file.recycle')}}
              </el-button>
            </template>
            <template v-else>
              <el-button class="filter-item el-button--mini" type="primary" icon="el-icon-back" @click="goToRecycleBin">
                {{$t('knowledge.file.return')}}
              </el-button>
            </template>
          </div>
        </div>
      </div>

      <!-- 正在上传状态 - 移到这里，在表格之上 -->
      <div class="uploading-container" v-if="uploadingFiles.length > 0">
        <div class="upload-status-title">{{$t('knowledge.file.uploading')}}</div>
        <div v-for="(file, index) in uploadingFiles" :key="'uploading-'+index" class="file-item uploading-item">
          <div class="file-icon">
            <i :class="getFileIconClass(file.name)"></i>
          </div>
          <div class="file-info">
            <div class="file-name">{{ file.name }}</div>
            <div class="file-meta">{{ formatFileSize(file.size) }}</div>
            <div class="upload-progress">
              <el-progress :percentage="file.percentage" :show-text="false"></el-progress>
              <span class="progress-text">{{ file.percentage }}%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 文件列表区域 - 添加loading状态 -->
      <div class="file-list-wrapper" v-loading="loading" :element-loading-text="$t('knowledge.file.uploadingStatus')"
           element-loading-spinner="el-icon-loading">
        <!-- 全选区域 -->
        <div class="select-all-row"
             v-if="(fileList.length > 0 && !isRecycleBin) || (recycleBinFiles.length > 0 && isRecycleBin)">
          <div class="selection-controls">
            <el-checkbox v-model="selectAll" @change="handleSelectAllChange">{{$t('knowledge.file.selectAll')}}</el-checkbox>
            <span v-if="getSelectedCount() > 0" class="selected-count">{{$t('knowledge.file.selectedItems', {count: getSelectedCount()})}}</span>
          </div>
          <div v-if="getSelectedCount() > 0" class="batch-actions">
            <el-button
              v-if="!isRecycleBin"
              class="delete-btn"
              icon="el-icon-delete"
              size="mini"
              @click="handleBatchDelete"
              :loading="batchActionLoading">
              {{$t('knowledge.file.delete')}}
            </el-button>
            <el-button
              v-else
              class="restore-btn"
              icon="el-icon-refresh-left"
              size="mini"
              @click="handleBatchRestore"
              :loading="batchActionLoading">
              {{$t('knowledge.file.restore')}}
            </el-button>
            <el-button class="cancel-btn" size="mini" @click="cancelSelection" :disabled="batchActionLoading">{{$t('knowledge.file.cancelSelection')}}</el-button>
          </div>
        </div>

        <!-- 文件列表 -->
        <div v-if="!isRecycleBin && fileList.length > 0" class="file-list">
          <div v-for="(file, index) in fileList" :key="index" class="file-item">
            <div class="file-checkbox">
              <el-checkbox v-model="file.selected" @change="handleItemSelectChange"></el-checkbox>
            </div>
            <div class="file-icon">
              <i :class="getFileIconClass(file.fileName)"></i>
            </div>
            <div class="file-info">
              <div class="file-name">
                {{ file.fileName }}
                <!-- 状态标签 -->
                <span v-if="file.miningStatus" :class="['file-status-label', getMiningStatusClass(file.miningStatus)]">
                  {{ getMiningStatusText(file.miningStatus) }}
                </span>
              </div>
              <div class="file-meta">
                <template v-if="file.fragmentCount">{{$t('knowledge.file.fragmentCount', {count: file.fragmentCount})}}</template>
                <template v-if="file.qaCount">{{$t('knowledge.file.qaCount', {count: file.qaCount})}}</template>
              </div>
            </div>
            <div class="file-status" v-if="file.status">
              <el-tag size="small" type="success" v-if="file.status === 'completed'">{{$t('knowledge.file.completed')}}</el-tag>
              <el-tag size="small" type="warning" v-else>{{$t('knowledge.file.processing')}}</el-tag>
            </div>
            <div class="file-status-text">
              {{ file.isEnabled === 1 ? $t('knowledge.file.enabled') : $t('knowledge.file.disabled') }}
            </div>
            <div class="file-enable-switch">
              <el-switch
                v-model="file.isEnabled"
                :active-value="1"
                :inactive-value="0"
                @change="handleEnableChange(file)"
                active-color="#0d47a1"
                inactive-color="#ff4949">
              </el-switch>
            </div>
            <div class="file-actions">
              <el-dropdown trigger="click" @command="(command) => handleCommand(command, file)">
                <span class="el-dropdown-link">
                  <i class="el-icon-more"></i>
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="delete">{{$t('knowledge.file.delete')}}</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
        </div>

        <!-- 回收站文件列表 -->
        <div v-if="isRecycleBin && recycleBinFiles.length > 0" class="file-list">
          <div v-for="(file, index) in recycleBinFiles" :key="index" class="file-item">
            <div class="file-checkbox">
              <el-checkbox v-model="file.selected" @change="handleItemSelectChange"></el-checkbox>
            </div>
            <div class="file-icon">
              <i :class="getFileIconClass(file.fileName)"></i>
            </div>
            <div class="file-info">
              <div class="file-name">{{ file.fileName }}</div>
              <div class="file-meta">
                <template v-if="file.fragmentCount">{{ file.fragmentCount }}片段</template>
                <template v-if="file.qaCount">{{ file.qaCount }}个QA</template>
              </div>
            </div>
            <div class="file-actions">
              <el-dropdown trigger="click" @command="(command) => handleRecycleBinCommand(command, file)">
                <span class="el-dropdown-link">
                  <i class="el-icon-more"></i>
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="restore">{{$t('knowledge.file.restore')}}</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="fileList.length === 0 && !isRecycleBin && !loading && uploadingFiles.length === 0"
             class="empty-state">
          <div class="empty-content">
            <p>{{$t('knowledge.file.empty')}}</p>
          </div>
        </div>

        <!-- 回收站空状态 -->
        <div v-if="isRecycleBin && recycleBinFiles.length === 0 && !loading" class="empty-state">
          <div class="empty-content">
            <p>{{$t('knowledge.file.recycleEmpty')}}</p>
          </div>
        </div>
      </div>

      <!-- 分页区域 -->
      <div class="pagination-container" v-if="total > 0">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </div>
    </div>

    <!-- 隐藏的文件上传input -->
    <input
      type="file"
      ref="fileInput"
      style="display: none;"
      @change="handleFileInputChange"
      multiple
      accept="application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/plain,image/*"
    />
  </d2-container>
</template>

<script>
import api from "@/api";
import {mapActions} from "vuex";

export default {
  name: "file",
  data() {
    return {
      searchForm: {
        fileName: '',
        deptId: ''
      },
      deptOptions: [],
      isAdmin: false,
      fileList: [],
      uploadingFiles: [],
      recycleBinFiles: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      loading: false,
      batchActionLoading: false,
      selectAll: false,
      isRecycleBin: false
    }
  },
  created() {
    this.fetchData();
    this.getCurrentUserInfo();
  },
  methods: {
    ...mapActions("bootAdmin/user", [
      "currentUserInfo",
    ]),
    getCurrentUserInfo() {
      let _self = this;
      _self.currentUserInfo().then((result) => {
        this.isAdmin = result.isAdmin;
        if (this.isAdmin) {
          this.getDeptList();
        }
      });
    },
    getDeptList() {
      api.DEPT_ALL_LIST().then(res => {
        this.deptOptions = res || [];
      });
    },
    fetchData() {
      this.loading = true;
      const params = {
        fileName: this.searchForm.fileName,
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        deptId: this.searchForm.deptId
      };

      // 根据是否在回收站模式下获取不同的数据
      if (this.isRecycleBin) {
        // 回收站模式，查询delFlag=1的数据
        params.delFlag = 1;
      } else {
        // 正常模式，查询delFlag=0的数据
        params.delFlag = 0;
      }

      api.fileListPage(params).then(res => {
        const records = res.records || [];
        if (this.isRecycleBin) {
          this.recycleBinFiles = records.map(item => ({...item, selected: false}));
        } else {
          this.fileList = records.map(item => ({...item, selected: false}));
        }
        this.total = Number(res.total) || 0;
        // 重置全选状态
        this.selectAll = false;
      }).finally(() => {
        this.loading = false;
      });
    },
    handleSearch() {
      this.currentPage = 1;
      this.fetchData();
    },
    handleUpload() {
      // 点击隐藏的文件上传输入框
      this.$refs.fileInput.click();
    },
    handleFileInputChange(event) {
      const files = event.target.files;
      if (!files || files.length === 0) return;

      // 文件大小限制：10MB
      const maxSize = 10 * 1024 * 1024; // 10MB

      // 处理每个选择的文件
      Array.from(files).forEach(file => {
        // 检查文件大小是否超过限制
        if (file.size > maxSize) {
          this.$message.error(this.$t('knowledge.file.fileSizeExceeded', {maxSize: '10MB'}));
          return;
        }
        this.uploadFile(file);
      });

      // 清空input，以便可以重复上传相同的文件
      this.$refs.fileInput.value = '';
    },
    uploadFile(file) {
      // 添加到正在上传的文件列表
      const uploadingFile = {
        name: file.name,
        size: file.size,
        percentage: 0
      };
      this.uploadingFiles.push(uploadingFile);

      // 模拟上传进度
      const progressInterval = setInterval(() => {
        if (uploadingFile.percentage < 99) {
          uploadingFile.percentage += 10;
        }
      }, 300);

      const formData = new FormData();
      formData.append('file', file);

      // 如果有部门ID，添加到请求中
      if (this.searchForm.deptId) {
        formData.append('deptId', this.searchForm.deptId);
      }

      api.ossFileUpload(formData, {
        'Content-Type': 'multipart/form-data'
      }).then(res => {
        clearInterval(progressInterval);
        uploadingFile.percentage = 100;

        // 文件上传成功后，调用保存文件信息的API
        return api.fileSave({
          fileName: res.fileName,
          fileKey: res.fileKey,
          deptId: this.searchForm.deptId
        });
      }).then(() => {
        // 延迟移除上传状态显示
        setTimeout(() => {
          this.uploadingFiles = this.uploadingFiles.filter(item => item !== uploadingFile);
          this.$message.success(this.$t('knowledge.file.uploadSuccess'));
          this.fetchData(); // 刷新文件列表
        }, 500);
      }).catch(error => {
        clearInterval(progressInterval);
        this.uploadingFiles = this.uploadingFiles.filter(item => item !== uploadingFile);
        this.$message.error(this.$t('knowledge.file.uploadFail', {msg: error.message || this.$t('knowledge.file.actionFail')}));
      });
    },
    handleSizeChange(size) {
      this.pageSize = size;
      this.fetchData();
    },
    handleCurrentChange(page) {
      this.currentPage = page;
      this.fetchData();
    },
    handleSelectAllChange(val) {
      const files = this.isRecycleBin ? this.recycleBinFiles : this.fileList;
      files.forEach(file => {
        file.selected = val;
      });
    },
    handleItemSelectChange() {
      const files = this.isRecycleBin ? this.recycleBinFiles : this.fileList;
      this.selectAll = files.length > 0 && files.every(file => file.selected);
    },
    getFileIconClass(fileName) {
      if (!fileName) return 'el-icon-document';

      // 根据文件扩展名返回不同的图标类
      const extension = fileName.split('.').pop().toLowerCase();
      const iconMap = {
        'pdf': 'el-icon-document',
        'doc': 'el-icon-document',
        'docx': 'el-icon-document',
        'xls': 'el-icon-document',
        'xlsx': 'el-icon-document',
        'ppt': 'el-icon-document',
        'pptx': 'el-icon-document',
        'jpg': 'el-icon-picture',
        'jpeg': 'el-icon-picture',
        'png': 'el-icon-picture',
        'gif': 'el-icon-picture',
        'txt': 'el-icon-document'
      };

      return iconMap[extension] || 'el-icon-document';
    },
    formatFileSize(size) {
      if (!size) return '';

      const kb = 1024;
      const mb = kb * 1024;

      if (size < kb) {
        return size + ' B';
      } else if (size < mb) {
        return (size / kb).toFixed(2) + ' KB';
      } else {
        return (size / mb).toFixed(2) + ' MB';
      }
    },
    handleEnableChange(file) {
      // 记录原状态
      const oldValue = file.isEnabled;
      // 正确调用fileUpdate(id, data)
      api.fileUpdate(file.id, {
        id: file.id,
        isEnabled: file.isEnabled,
        fileName: file.fileName,
        qaStatus: file.isEnabled === 1 ? 0 : 1,
      }).then(() => {
        this.$message.success(file.isEnabled === 1 ? this.$t('knowledge.file.enableSuccess') : this.$t('knowledge.file.disableSuccess'));
      }).catch(() => {
        // 回滚状态
        file.isEnabled = oldValue === 1 ? 0 : 1;
        this.$message.error(this.$t('knowledge.file.actionFail'));
      });
    },
    handleCommand(command, file) {
      switch (command) {
        case 'delete':
          this.$confirm(this.$t('knowledge.file.deleteConfirm'), this.$t('knowledge.file.title'), {
            confirmButtonText: this.$t('knowledge.file.delete'),
            cancelButtonText: this.$t('knowledge.file.cancel'),
            type: 'warning'
          }).then(() => {
            // 调用api.fileUpdate将delFlag设为1
            api.fileUpdate(file.id, {
              id: file.id,
              fileName: file.fileName,
              delFlag: 1,
              qaStatus: 1,
              deptId: this.searchForm.deptId
            }).then(() => {
              this.$message.success(this.$t('knowledge.file.deleteSuccess'));
              this.fetchData();
            })
          }).catch(() => {
          });
          break;
      }
    },
    handleRecycleBinCommand(command, file) {
      switch (command) {
        case 'restore':
          this.$confirm(this.$t('knowledge.file.restoreConfirm'), this.$t('knowledge.file.title'), {
            confirmButtonText: this.$t('knowledge.file.restore'),
            cancelButtonText: this.$t('knowledge.file.cancel'),
            type: 'info'
          }).then(() => {
            // 调用恢复API (将delFlag设为0)
            const params = {
              id: file.id,
              delFlag: 0
            };
            api.fileUpdate(file.id, {
              id: file.id,
              fileName: file.fileName,
              delFlag: 0,
              isEnabled: 1,
              qaStatus: 0,
              deptId: this.searchForm.deptId
            }).then(() => {
              this.$message.success(this.$t('knowledge.file.restoreSuccess'));
              this.fetchData();
            })
            this.fetchData();
          }).catch(() => {
          });
          break;
      }
    },
    goToRecycleBin() {
      this.isRecycleBin = !this.isRecycleBin;
      this.currentPage = 1;
      this.searchForm.fileName = ''; // 清空搜索条件
      this.fetchData();
    },
    getMiningStatusText(status) {
      return this.$t(`knowledge.file.miningStatus.${status}`);
    },
    getMiningStatusClass(status) {
      switch (status) {
        case 1:
          return 'status-error';
        case 2:
          return 'status-running';
        case 3:
          return 'status-success';
        default:
          return '';
      }
    },
    getSelectedCount() {
      const selectedFiles = this.isRecycleBin ? this.recycleBinFiles : this.fileList;
      return selectedFiles.filter(file => file.selected).length;
    },
    handleBatchDelete() {
      const selectedFiles = this.isRecycleBin ? this.recycleBinFiles : this.fileList;
      const selectedCount = selectedFiles.filter(file => file.selected).length;
      if (selectedCount === 0) {
        this.$message.warning(this.$t('knowledge.file.noItemsSelected'));
        return;
      }

      this.$confirm(this.$t('knowledge.file.batchDeleteConfirm'), this.$t('knowledge.file.title'), {
        confirmButtonText: this.$t('knowledge.file.delete'),
        cancelButtonText: this.$t('knowledge.file.cancel'),
        type: 'warning'
      }).then(() => {
        // 设置loading状态
        this.batchActionLoading = true;

        // 获取选中的文件完整对象数组
        const selectedFileObjects = selectedFiles.filter(file => file.selected);

        // 为所有选中的文件对象添加删除标记
        const updateData = selectedFileObjects.map(file => {
          return {
            id: file.id,
            fileName: file.fileName,
            delFlag: 1,
            qaStatus: 1,
            deptId: this.searchForm.deptId
          };
        });

        // 发送完整对象数组
        api.fileBatchUpdate(updateData).then(() => {
          this.$message.success(this.$t('knowledge.file.batchDeleteSuccess'));
          this.selectAll = false; // 重置全选框状态
          this.fetchData();
        }).catch(error => {
          this.$message.error(this.$t('knowledge.file.actionFail', {msg: error.message || this.$t('knowledge.file.actionFail')}));
        }).finally(() => {
          // 无论成功或失败都需要关闭loading状态
          this.batchActionLoading = false;
        });
      }).catch(() => {
        // 用户取消删除
      });
    },
    cancelSelection() {
      const files = this.isRecycleBin ? this.recycleBinFiles : this.fileList;
      files.forEach(file => {
        file.selected = false;
      });
      this.selectAll = false; // 取消全选
    },
    handleBatchRestore() {
      const selectedFiles = this.recycleBinFiles;
      const selectedCount = selectedFiles.filter(file => file.selected).length;
      if (selectedCount === 0) {
        this.$message.warning(this.$t('knowledge.file.noItemsSelected'));
        return;
      }

      this.$confirm(this.$t('knowledge.file.batchRestoreConfirm'), this.$t('knowledge.file.title'), {
        confirmButtonText: this.$t('knowledge.file.restore'),
        cancelButtonText: this.$t('knowledge.file.cancel'),
        type: 'info'
      }).then(() => {
        // 设置loading状态
        this.batchActionLoading = true;

        // 获取选中的文件完整对象数组
        const selectedFileObjects = selectedFiles.filter(file => file.selected);

        // 为所有选中的文件对象添加恢复标记
        const updateData = selectedFileObjects.map(file => {
          return {
            id: file.id,
            fileName: file.fileName,
            delFlag: 0,
            isEnabled: 1,
            qaStatus: 0,
            deptId: this.searchForm.deptId
          };
        });

        // 发送完整对象数组
        api.fileBatchUpdate(updateData).then(() => {
          this.$message.success(this.$t('knowledge.file.batchRestoreSuccess'));
          this.selectAll = false; // 重置全选框状态
          this.fetchData();
        }).catch(error => {
          this.$message.error(this.$t('knowledge.file.actionFail', {msg: error.message || this.$t('knowledge.file.actionFail')}));
        }).finally(() => {
          // 无论成功或失败都需要关闭loading状态
          this.batchActionLoading = false;
        });
      }).catch(() => {
        // 用户取消恢复
      });
    }
  }
}
</script>

<style scoped lang="scss">
.file-page {
  padding: 20px;
  background: #f7f8fa;
  min-height: calc(100vh - 120px);

  .page-content-wrapper {
    width: 100%;
    max-width: 900px;
    margin: 0 auto;
    padding: 0;
    position: relative;
  }

  .file-header {
    padding-left: 0;
    margin-left: 0;

    .file-title-container {
      margin-bottom: 30px;
      text-align: left;
      padding-left: 0;
      margin-left: -20px;

      .file-title {
        font-size: 20px;
        color: #303133;
        margin: 0 0 8px 0;
      }

      .file-subtitle {
        font-size: 14px;
        color: #909399;
        margin: 0;
      }
    }

    .search-and-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30px;
      padding-left: 0;

      .search-box {
        width: 500px; // 增加宽度以适应下拉框

        .custom-search-wrapper {
          display: flex;
          width: 100%;

          .customer-select {
            width: 140px;
            margin-right: 10px;

            ::v-deep .el-input__inner {
              border-radius: 4px;
              border: 1px solid #DCDFE6;
              height: 36px;
            }

            ::v-deep .el-input.is-focus .el-input__inner {
              border-color: #0d47a1;
            }
          }

          .search-input-long {
            flex: 1;

            ::v-deep .el-input__inner {
              border: 1px solid #DCDFE6;
              border-radius: 4px;
              height: 36px;
            }
          }
        }
      }

      .action-buttons {
        display: flex;
        gap: 10px;
      }
    }
  }

  // 正在上传容器的样式 - 独立出来作为表格外的容器
  .uploading-container {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid #EBEEF5;

    .upload-status-title {
      margin: 0 0 15px 0;
      font-size: 14px;
      font-weight: 500;
    }

    .uploading-item {
      display: flex;
      align-items: center;
      padding: 5px 15px;
      border-radius: 6px;
      background-color: #f0f8ff;
      margin-bottom: 6px;
      border: 1px solid #e1ebf6;

      .file-icon {
        width: 15px;
        height: 15px;
        background: #fff;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;

        i {
          font-size: 16px;
          color: #409EFF;
        }
      }

      .file-info {
        flex-grow: 1;

        .file-name {
          font-size: 14px;
          color: #303133;
          margin-bottom: 4px;
          font-weight: 500;
        }

        .file-meta {
          font-size: 12px;
          color: #606266;
        }

        .upload-progress {
          display: flex;
          align-items: center;
          margin-top: 8px;

          .el-progress {
            flex-grow: 1;
            margin-right: 10px;
          }

          .progress-text {
            font-size: 12px;
            color: #606266;
            white-space: nowrap;
            font-weight: 500;
          }
        }
      }
    }
  }

  .file-list-wrapper {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    padding: 20px;
    margin-bottom: 20px;
    margin-left: 0;
    margin-right: 0;
    border: 1px solid #EBEEF5;
    min-height: 200px; // 添加最小高度，使loading图标在空数据时居中显示
    position: relative; // 确保loading覆盖层定位正确

    .select-all-row {
        padding: 10px 0;
        border-bottom: 1px solid #EBEEF5;
        margin-bottom: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .selection-controls {
          margin-left: 5px;
          display: flex;
          align-items: center;

          .selected-count {
            font-size: 14px;
            color: #606266;
            font-weight: 500;
            margin-left: 20px;
          }
        }

        .batch-actions {
          display: flex;
          align-items: center;
          gap: 10px;

          .delete-btn {
            background-color: #f56c6c;
            border-color: #f56c6c;
            color: #fff;
            &:hover {
              background-color: #f78989;
              border-color: #f78989;
            }
          }

          .restore-btn {
            background-color: #67c23a;
            border-color: #67c23a;
            color: #fff;
            &:hover {
              background-color: #85ce61;
              border-color: #85ce61;
            }
          }

          .cancel-btn {
            background-color: #e0e0e0;
            border-color: #e0e0e0;
            color: #303133;
            &:hover {
              background-color: #d0d0d0;
              border-color: #d0d0d0;
            }
          }
        }
      }

    .file-list {
      display: flex;
      flex-direction: column;
    }

    .file-item {
      display: flex;
      align-items: center;
      padding: 12px 5px;
      border-bottom: none;
      border-radius: 4px;
      transition: all 0.2s ease;

      &:hover {
        background-color: #f9fafc;
        transform: translateY(-1px);
        box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.03);
        padding-left: 8px;
        padding-right: 8px;
      }

      &:last-child {
        border-bottom: none;
      }

      .file-checkbox {
        margin-right: 12px;
      }

      .file-icon {
        width: 15px;
        height: 15px;
        background: #f5f7fa;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;

        i {
          font-size: 16px;
          color: #606266;
        }
      }

      .file-info {
        flex-grow: 1;

        .file-name {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: 500;

          .file-status-label {
            display: inline-block;
            padding: 0 10px;
            height: 20px;
            line-height: 20px;
            border-radius: 12px;
            font-size: 10px;
            margin-left: 6px;
            font-weight: 400;
          }

          .status-success {
            background: #e6f9ed;
            color: #19b37c;
          }

          .status-running {
            background: #e6f0fa;
            color: #409EFF;
          }

          .status-error {
            background: #fbeaea;
            color: #f56c6c;
          }
        }

        .file-meta {
          font-size: 12px;
          color: #909399;
        }
      }

      .file-status {
        margin-right: 16px;
      }

      .file-status-text {
        margin-right: 8px;
        font-size: 12px;
        color: #606266;
      }

      .file-enable-switch {
        margin-right: 16px;
      }

      .file-actions {
        cursor: pointer;
        padding: 0 8px;

        i {
          font-size: 16px;
          color: #909399;
        }
      }
    }

    .empty-state {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;

      .empty-content {
        text-align: center;
        color: #909399;

        p {
          font-size: 16px;
          margin-top: 10px;
        }
      }
    }
  }

  .pagination-container {
    margin-top: 24px;
    display: flex;
    justify-content: center;
    margin-left: 0;
    margin-bottom: 20px;

    ::v-deep .el-pagination,
    ::v-deep .el-pagination__total,
    ::v-deep .el-pagination__sizes,
    ::v-deep .el-pagination__jump,
    ::v-deep .el-pagination__editor,
    ::v-deep .el-pager,
    ::v-deep .el-pager li {
      font-size: 12px !important;
    }

    ::v-deep .el-pagination__sizes .el-input__inner {
      font-size: 12px !important;
      height: 28px;
      line-height: 28px;
      padding: 0 20px 0 8px;
    }
  }
}

@media screen and (max-width: 768px) {
  .file-page {
    padding: 10px;

    .page-content-wrapper {
      padding: 0 10px;
      max-width: 100%;
    }

    .file-header {
      .search-and-actions {
        flex-direction: column;
        align-items: flex-start;

        .search-box {
          width: 100%;
          margin-bottom: 10px;

          .custom-search-wrapper {
            flex-direction: column;

            .customer-select {
              width: 100%;
              margin-right: 0;
              margin-bottom: 10px;
            }
          }
        }

        .action-buttons {
          width: 100%;
          justify-content: flex-start;
        }
      }
    }
  }
}
</style>
