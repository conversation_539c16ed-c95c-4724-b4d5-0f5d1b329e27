<template>
  <div id="app">
    <router-view/>
  </div>
</template>

<script>
import util from '@/libs/util'
import { mapActions } from 'vuex'

export default {
  name: 'app',
  watch: {
    '$i18n.locale': 'i18nHandle'
  },
  created () {
    this.i18nHandle(this.$i18n.locale)
  },
  mounted () {
    // 强制设置为rufit主题
    this.$store.dispatch('d2admin/theme/set', 'rufit')
  },
  methods: {
    ...mapActions('bootAdmin/menu', ['currentMenu']),
    i18nHandle (val, oldVal) {
      util.cookies.set('lang', val)
      document.querySelector('html').setAttribute('lang', val)

      // 当语言变更时，重新获取菜单以应用翻译
      // 但仅在不是登录页面时执行
      if (oldVal && oldVal !== val && this.$route && this.$route.path !== '/login') {
        this.currentMenu().then(() => {
          // 触发重新渲染，但不改变菜单折叠状态
          this.$nextTick(() => {
            // 通知vuex菜单已更新，但不触发折叠状态变化
            this.$store.dispatch('d2admin/menu/init')
          })
        })
      }
    }
  }
}
</script>

<style lang="scss">
@import '~@/assets/style/public-class.scss';
@import '~@/assets/style/theme/rufit/element-variables.scss';
@import '~@/assets/style/theme/rufit/fonts.scss';

/* 基础字体应用 */
html, body {
  font-family: $font-family-secondary;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style>
