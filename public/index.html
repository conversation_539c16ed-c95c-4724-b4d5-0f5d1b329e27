<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="icon" href="<%= BASE_URL %>image/theme/marsmind/logo/mars.svg" type="image/svg+xml">
    <!-- Google Fonts -->
    <link id="english-font" href="https://fonts.googleapis.com/css2?family=Roboto&display=swap" rel="stylesheet">
    <link id="cjk-fonts" href="https://fonts.googleapis.com/css2?family=Roboto&family=Noto+Sans+SC&family=Noto+Sans+JP&display=swap" rel="stylesheet">
    <script>
      // 根据语言动态加载字体
      document.addEventListener('DOMContentLoaded', function() {
        var userLang = navigator.language || navigator.userLanguage;
        var englishFont = document.getElementById('english-font');
        var cjkFonts = document.getElementById('cjk-fonts');
        
        if (userLang.startsWith('zh') || userLang.startsWith('ja')) {
          // 中文或日文环境
          englishFont.disabled = true;
          cjkFonts.disabled = false;
        } else {
          // 其他语言环境（默认英文）
          englishFont.disabled = false;
          cjkFonts.disabled = true;
        }
      });
    </script>
    <!-- 使用 CDN 加速的 CSS 文件，配置在 vue.config.js 下 -->
    <% for (var i in htmlWebpackPlugin.options.cdn&&htmlWebpackPlugin.options.cdn.css) { %>
    <link href="<%= htmlWebpackPlugin.options.cdn.css[i] %>" rel="preload" as="style">
    <link href="<%= htmlWebpackPlugin.options.cdn.css[i] %>" rel="stylesheet">
    <% } %>
    <!-- 使用 CDN 加速的 JS 文件，配置在 vue.config.js 下 -->
    <% for (var i in htmlWebpackPlugin.options.cdn&&htmlWebpackPlugin.options.cdn.js) { %>
    <link href="<%= htmlWebpackPlugin.options.cdn.js[i] %>" rel="preload" as="script">
    <% } %>
    <title><%= VUE_APP_TITLE %></title>
    <style>
      html, body, #app { height: 100%; margin: 0px; padding: 0px; width: 100%; }
      .d2-home { background-color: #303133; height: 100%; display: flex; flex-direction: column; }
      .d2-home__main { user-select: none; width: 100%; flex-grow: 1; display: flex; justify-content: center; align-items: center; flex-direction: column; }
      .d2-home__footer { width: 100%; flex-grow: 0; text-align: center; padding: 1em 0; }
      .d2-home__footer > a { font-size: 12px; color: #ABABAB; text-decoration: none; }
      .d2-home__loading { height: 32px; width: 32px; margin-bottom: 20px; }
    </style>
    <script>
      var _hmt = _hmt || [];
      var hmid = "bc38887aa5588add05a38704342ad7e8";
      (function() { var hm = document.createElement("script"); hm.src = "https://hm.baidu.com/hm.js?" + hmid; var s = document.getElementsByTagName("script")[0]; s.parentNode.insertBefore(hm, s);})();
    </script>
  </head>
  <body>
    <noscript>
      <strong>
        Sorry, D2Admin will not work properly without JavaScript support. Enable JavaScript for browsers and continue.
      </strong>
    </noscript>
    <div id="app">
      <div class="d2-home">
        <div class="d2-home__main">
          <img
            class="d2-home__loading"
            src="./image/loading/loading-spin.svg"
            alt="loading">
        </div>
        <div class="d2-home__footer">
          <a
            href="https://www.lufitglobalselling.com:8888"
            target="_blank">
              lufitglobalselling
          </a>
        </div>
      </div>
    </div>
    <!-- 使用 CDN 加速的 JS 文件，配置在 vue.config.js 下 -->
    <% for (var i in htmlWebpackPlugin.options.cdn&&htmlWebpackPlugin.options.cdn.js) { %>
    <script src="<%= htmlWebpackPlugin.options.cdn.js[i] %>"></script>
    <% } %>
  </body>
</html>
